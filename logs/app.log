2025-05-27T13:35:06.029+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 19520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T13:35:06.036+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T13:35:06.076+02:00  INFO 19520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T13:35:06.076+02:00  INFO 19520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T13:35:06.795+02:00  INFO 19520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T13:35:06.878+02:00  INFO 19520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 71 ms. Found 10 JPA repository interfaces.
2025-05-27T13:35:07.438+02:00  INFO 19520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T13:35:07.450+02:00  INFO 19520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T13:35:07.450+02:00  INFO 19520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T13:35:07.490+02:00  INFO 19520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T13:35:07.490+02:00  INFO 19520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1414 ms
2025-05-27T13:35:07.667+02:00  INFO 19520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T13:35:07.729+02:00  INFO 19520 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T13:35:07.760+02:00  INFO 19520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T13:35:08.043+02:00  INFO 19520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T13:35:08.073+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T13:35:08.969+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@27bc26d0
2025-05-27T13:35:08.970+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T13:35:09.261+02:00  INFO 19520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T13:35:10.183+02:00  INFO 19520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T13:35:12.439+02:00  INFO 19520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:35:12.481+02:00  WARN 19520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T13:35:12.702+02:00  INFO 19520 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T13:35:14.346+02:00  WARN 19520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T13:35:14.378+02:00  INFO 19520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T13:35:14.587+02:00  INFO 19520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T13:35:15.036+02:00  INFO 19520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T13:35:15.070+02:00  INFO 19520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T13:35:15.076+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.464 seconds (process running for 10.453)
2025-05-27T13:39:34.805+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T13:39:34.805+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T13:39:34.806+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T13:39:41.685+02:00  WARN 19520 --- [http-nio-8080-exec-7] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T13:39:41.690+02:00  WARN 19520 --- [http-nio-8080-exec-7] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T13:39:48.587+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:39:48.587644700
2025-05-27T13:39:48.661+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:39:48.587644700 using time unit HOUR
2025-05-27T13:39:48.661+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T14:39:48.587644700
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:48.719157200
2025-05-27T13:39:48.798+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:48.719157200 using time unit DAY
2025-05-27T13:39:48.798+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:39:48.877+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:39:48.957+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:39:48.957+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:56.039+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T13:39:56.103+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T13:39:56.103+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:56.168250800
2025-05-27T13:39:56.232+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:56.168250800 using time unit DAY
2025-05-27T13:39:56.232+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:39:56.370+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:39:56.386+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:03.862+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999
2025-05-27T13:40:03.926+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999 using time unit HOUR
2025-05-27T13:40:03.926+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-24T23:00 to 2025-05-26T00:59:59.999999999
2025-05-27T13:40:03.993+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-24T23:00:00.301157 = 1531.142, Last reading: 2025-05-26T00:00:00.206583 = 1536.977
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=2.3669434, 01:00 - 02:00=1.0090332, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.0059814453, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0059814453, 10:00 - 11:00=0.005004883, 11:00 - 12:00=0.005004883, 12:00 - 13:00=0.0059814453, 13:00 - 14:00=0.005004883, 14:00 - 15:00=0.0059814453, 15:00 - 16:00=0.005004883, 16:00 - 17:00=0.0010986328, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.0, 20:00 - 21:00=0.0, 21:00 - 22:00=0.0, 22:00 - 23:00=0.0, 23:00 - 00:00=0.0}
2025-05-27T13:40:03.995+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:40:03.995926800
2025-05-27T13:40:04.062+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:40:03.995926800 using time unit DAY
2025-05-27T13:40:04.063+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:40:04.203+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:40:04.203+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:40:04.266+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:40:04.266+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T13:40:11.903+02:00  INFO 19520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T13:52:24.410+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 18208 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T13:52:24.412+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T13:52:24.448+02:00  INFO 18208 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T13:52:24.448+02:00  INFO 18208 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T13:52:25.074+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T13:52:25.147+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 67 ms. Found 10 JPA repository interfaces.
2025-05-27T13:52:25.692+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T13:52:25.706+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T13:52:25.706+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T13:52:25.751+02:00  INFO 18208 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T13:52:25.751+02:00  INFO 18208 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1303 ms
2025-05-27T13:52:25.968+02:00  INFO 18208 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T13:52:26.017+02:00  INFO 18208 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T13:52:26.045+02:00  INFO 18208 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T13:52:26.288+02:00  INFO 18208 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T13:52:26.314+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T13:52:27.134+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2a6c8c8a
2025-05-27T13:52:27.135+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T13:52:27.419+02:00  INFO 18208 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T13:52:28.282+02:00  INFO 18208 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T13:52:30.548+02:00  INFO 18208 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:52:30.580+02:00  WARN 18208 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T13:52:30.785+02:00  INFO 18208 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T13:52:32.451+02:00  WARN 18208 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T13:52:32.481+02:00  INFO 18208 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T13:52:32.677+02:00  INFO 18208 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T13:52:33.097+02:00  INFO 18208 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T13:52:33.129+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T13:52:33.136+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.112 seconds (process running for 9.815)
2025-05-27T13:54:47.730+02:00  INFO 18208 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T13:54:47.730+02:00  INFO 18208 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T13:54:47.731+02:00  INFO 18208 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T13:54:51.252+02:00  INFO 18208 --- [Thread-8] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.253+02:00 ERROR 18208 --- [Thread-8] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@537a1d4f: Master is null.
2025-05-27T13:54:51.671+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.671+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4dbef09a: Master is null.
2025-05-27T13:54:51.730+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.730+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@588e6179: Master is null.
2025-05-27T13:54:51.792+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.792+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5c022f6b: Master is null.
2025-05-27T13:54:51.852+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.852+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1d876f05: Master is null.
2025-05-27T13:54:52.138+02:00  INFO 18208 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.138+02:00 ERROR 18208 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@7719cb78: Master is null.
2025-05-27T13:54:52.505+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.505+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@20a1c243: Master is null.
2025-05-27T13:54:52.565+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.565+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@10dfe7fb: Master is null.
2025-05-27T13:54:52.626+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.626+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@71527bd0: Master is null.
2025-05-27T13:54:52.687+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.687+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2d8e95a0: Master is null.
2025-05-27T13:54:52.998+02:00  INFO 18208 --- [Thread-12] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.999+02:00 ERROR 18208 --- [Thread-12] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@360cfcd8: Master is null.
2025-05-27T13:54:53.327+02:00  INFO 18208 --- [Thread-13] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.327+02:00 ERROR 18208 --- [Thread-13] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6ededa43: Master is null.
2025-05-27T13:54:53.704+02:00  INFO 18208 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.704+02:00 ERROR 18208 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6d86135e: Master is null.
2025-05-27T13:54:53.756+02:00  INFO 18208 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.756+02:00 ERROR 18208 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@66967642: Master is null.
2025-05-27T13:54:53.818+02:00  INFO 18208 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.818+02:00 ERROR 18208 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5d16b0ef: Master is null.
2025-05-27T13:54:54.145+02:00  INFO 18208 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:54 CEST 2025
2025-05-27T13:54:54.145+02:00 ERROR 18208 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@514ef9cf: Master is null.
2025-05-27T13:54:54.435+02:00  INFO 18208 --- [Thread-16] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:54 CEST 2025
2025-05-27T13:54:54.435+02:00 ERROR 18208 --- [Thread-16] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5c559cc5: Master is null.
2025-05-27T13:54:54.818+02:00  INFO 18208 --- [Thread-17] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:54 CEST 2025
2025-05-27T13:54:54.818+02:00 ERROR 18208 --- [Thread-17] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6217b890: Master is null.
2025-05-27T13:54:56.683+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:54:56.683940300
2025-05-27T13:54:56.751+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:54:56.683940300 using time unit HOUR
2025-05-27T13:54:56.751+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T14:54:56.683940300
2025-05-27T13:54:56.817+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-27T13:54:56.818+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:54:56.820+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:54:56.820+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:54:56.822+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-27T13:54:56.824+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:54:56.824402
2025-05-27T13:54:56.891+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:54:56.824402 using time unit DAY
2025-05-27T13:54:56.891+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:54:56.962+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:54:56.972+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:54:57.049+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:54:57.049+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:54:57.125+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:54:57.125+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:54:57.127+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:54:57.127+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:54:57.130+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:54:57.130+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:54:57.130+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:55:07.539+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T13:55:08.784+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T13:55:08.784+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T13:55:08.850+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:55:08.850+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:55:08.851+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:55:08.851+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T13:55:08.852+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T13:55:19.072+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-04-01T00:00 to 2025-04-30T23:59:59.999999999
2025-05-27T13:55:20.297+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-04-01T00:00 to 2025-04-30T23:59:59.999999999 using time unit DAY
2025-05-27T13:55:20.297+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-03-31T00:00 to 2025-05-01T23:59:59
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 755 readings in extended date range
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 30 periods: [01/04, 02/04, 03/04, 04/04, 05/04, 06/04, 07/04, 08/04, 09/04, 10/04, 11/04, 12/04, 13/04, 14/04, 15/04, 16/04, 17/04, 18/04, 19/04, 20/04, 21/04, 22/04, 23/04, 24/04, 25/04, 26/04, 27/04, 28/04, 29/04, 30/04]
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 30 periods: [01/04, 02/04, 03/04, 04/04, 05/04, 06/04, 07/04, 08/04, 09/04, 10/04, 11/04, 12/04, 13/04, 14/04, 15/04, 16/04, 17/04, 18/04, 19/04, 20/04, 21/04, 22/04, 23/04, 24/04, 25/04, 26/04, 27/04, 28/04, 29/04, 30/04]
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-03-31T00:00:00.048771 = 1223.0, Last reading: 2025-05-01T23:00:00.048956 = 1402.753
2025-05-27T13:55:20.367+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/04=9.3029785, 02/04=0.044921875, 03/04=10.106079, 04/04=0.0, 05/04=10.28894, 06/04=0.063964844, 07/04=10.23999, 08/04=0.06201172, 09/04=10.606079, 10/04=8.635986, 11/04=0.0, 12/04=10.42395, 13/04=0.123046875, 14/04=0.046020508, 15/04=10.25, 16/04=0.052978516, 17/04=0.0, 18/04=0.0, 19/04=10.243042, 20/04=12.033936, 21/04=7.2769775, 22/04=9.722046, 23/04=10.009033, 24/04=9.427002, 25/04=0.12207031, 26/04=9.983887, 27/04=9.242065, 28/04=0.043945312, 29/04=0.0, 30/04=0.0}
2025-05-27T13:55:25.133+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:55:25.133382800
2025-05-27T13:55:26.305+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:55:25.133382800 using time unit DAY
2025-05-27T13:55:26.305+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:55:26.374+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:55:30.743+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999
2025-05-27T13:55:31.960+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:55:31.961+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2023-12-01T00:00 to 2025-01-31T23:59:59
2025-05-27T13:55:32.041+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 5439 readings in extended date range
2025-05-27T13:55:32.041+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:32.041+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:32.042+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-06-13T18:00:00.262571 = 385.49, Last reading: 2025-01-31T23:00:00.047367 = 1027.671
2025-05-27T13:55:32.046+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=49.399017, Jul=118.014984, Ago=69.617004, Sep=75.75897, Oct=98.82501, Nov=78.252014, Dic=75.276}
2025-05-27T13:55:32.046+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:55:32.047+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=49.399017, Jul=118.014984, Ago=69.617004, Sep=75.75897, Oct=98.82501, Nov=78.252014, Dic=75.276}
2025-05-27T13:55:33.832+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:55:35.071+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:55:35.071+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:55:35.147+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:55:35.147+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:35.148+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:35.148+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:55:35.152+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:55:35.152+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:55:35.152+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:57:34.920+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999
2025-05-27T13:57:36.266+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999 using time unit HOUR
2025-05-27T13:57:36.266+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-24T23:00 to 2025-05-26T00:59:59.999999999
2025-05-27T13:57:36.330+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:57:36.330+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:57:36.334+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:57:36.334+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-24T23:00:00.301157 = 1531.142, Last reading: 2025-05-26T00:00:00.206583 = 1536.977
2025-05-27T13:57:36.335+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=2.3669434, 01:00 - 02:00=1.0090332, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.0059814453, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0059814453, 10:00 - 11:00=0.005004883, 11:00 - 12:00=0.005004883, 12:00 - 13:00=0.0059814453, 13:00 - 14:00=0.005004883, 14:00 - 15:00=0.0059814453, 15:00 - 16:00=0.005004883, 16:00 - 17:00=0.0010986328, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.0, 20:00 - 21:00=0.0, 21:00 - 22:00=0.0, 22:00 - 23:00=0.0, 23:00 - 00:00=0.0}
2025-05-27T14:00:40.726+02:00  INFO 18208 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 48 class path changes (0 additions, 0 deletions, 48 modifications)
2025-05-27T14:00:40.729+02:00  INFO 18208 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:00:40.732+02:00  INFO 18208 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:00:40.737+02:00  INFO 18208 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:00:40.739+02:00  INFO 18208 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T14:00:41.032+02:00  INFO 18208 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T14:00:41.119+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 18208 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:00:41.119+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:00:41.377+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:00:41.412+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 10 JPA repository interfaces.
2025-05-27T14:00:41.569+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:00:41.571+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:00:41.571+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:00:41.594+02:00  INFO 18208 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:00:41.594+02:00  INFO 18208 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 473 ms
2025-05-27T14:00:41.677+02:00  INFO 18208 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:00:41.679+02:00  INFO 18208 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:00:41.689+02:00  INFO 18208 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:00:41.690+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T14:00:42.311+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2ac4e4bc
2025-05-27T14:00:42.311+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T14:00:42.550+02:00  INFO 18208 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:00:42.730+02:00  INFO 18208 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:00:44.873+02:00  INFO 18208 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:00:44.893+02:00  WARN 18208 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:00:45.240+02:00  WARN 18208 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:00:45.259+02:00  INFO 18208 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:00:45.377+02:00  INFO 18208 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:00:45.596+02:00  INFO 18208 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:00:45.608+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:00:45.610+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.528 seconds (process running for 502.289)
2025-05-27T14:00:45.940+02:00  INFO 18208 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:01:21.842+02:00  INFO 18208 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:01:21.845+02:00  INFO 18208 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:01:21.847+02:00  INFO 18208 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:01:21.847+02:00  INFO 18208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T14:01:22.146+02:00  INFO 18208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T14:01:23.925+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:01:23.927+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:01:23.970+02:00  INFO 15520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T14:01:23.971+02:00  INFO 15520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T14:01:24.587+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:01:24.662+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 68 ms. Found 10 JPA repository interfaces.
2025-05-27T14:01:25.173+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:01:25.184+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:01:25.184+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:01:25.221+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:01:25.221+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1250 ms
2025-05-27T14:01:25.395+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:01:25.452+02:00  INFO 15520 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T14:01:25.479+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:01:25.716+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:01:25.746+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T14:01:26.565+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5ee06231
2025-05-27T14:01:26.567+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T14:01:26.871+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:01:27.828+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:01:30.093+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:01:30.123+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:01:30.328+02:00  INFO 15520 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T14:01:31.943+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:01:31.974+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:01:32.172+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:01:32.622+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:01:32.661+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:01:32.667+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.125 seconds (process running for 9.866)
2025-05-27T14:01:42.740+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:01:42.740+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:01:42.741+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T14:01:53.753+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:01:53.753663900
2025-05-27T14:01:53.832+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:01:53.753663900 using time unit HOUR
2025-05-27T14:01:53.832+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:01:53.753663900
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:01:53.898207800
2025-05-27T14:01:53.959+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:01:53.898207800 using time unit DAY
2025-05-27T14:01:53.959+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:01:54.042+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:01:54.042+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:01:54.042+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:01:54.043+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:01:54.044+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:01:54.053+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:01:54.126+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:01:54.126+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:01:54.201+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:01:54.202+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:01:54.202+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:01:54.203+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:01:54.207+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:01:54.207+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:01:54.207+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:01:59.938+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:02:01.157+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:02:01.157+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:02:01.221+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:02:01.221+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:01.230+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:01.230+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:02:01.231+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:02:03.307+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:03.307386300
2025-05-27T14:02:04.339+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:02:04.467+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:03.307386300 using time unit DAY
2025-05-27T14:02:04.468+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:04.536+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:04.984+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:04.984309400
2025-05-27T14:02:05.585+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:02:05.585+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:05.690+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:05.690+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:02:05.690+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:05.794+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:02:05.821+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:04.984309400 using time unit DAY
2025-05-27T14:02:05.828+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:07.015+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:02:07.015+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:02:09.477+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:02:10.694+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:02:10.694+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:13.835+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:13.835092200
2025-05-27T14:02:14.998+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:13.835092200 using time unit DAY
2025-05-27T14:02:14.998+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:18.530+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:02:19.750+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:02:19.750+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:02:19.827+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:02:19.827+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:19.827+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:19.828+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:19.831+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:19.831+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:02:19.831+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:21.806+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:21.806956600
2025-05-27T14:02:22.951+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:21.806956600 using time unit DAY
2025-05-27T14:02:22.951+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:23.018+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:27.221+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:02:28.429+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:02:28.429+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:02:28.495+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:02:28.495+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:28.496+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:28.496+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:02:28.496+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:02:30.685+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:30.685790400
2025-05-27T14:02:31.876+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:30.685790400 using time unit HOUR
2025-05-27T14:02:31.876+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:02:30.685790400
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:02:34.107+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:34.107231800
2025-05-27T14:02:35.284+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:34.107231800 using time unit HOUR
2025-05-27T14:02:35.284+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:02:34.107231800
2025-05-27T14:02:35.349+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:03:28.188+02:00  INFO 15520 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 47 class path changes (0 additions, 0 deletions, 47 modifications)
2025-05-27T14:03:28.191+02:00  INFO 15520 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:03:28.194+02:00  INFO 15520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:03:28.199+02:00  INFO 15520 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:03:28.202+02:00  INFO 15520 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T14:03:28.493+02:00  INFO 15520 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T14:03:28.563+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:03:28.563+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:03:28.802+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:03:28.835+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 10 JPA repository interfaces.
2025-05-27T14:03:28.950+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:03:28.950+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:03:28.950+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:03:28.969+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:03:28.969+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 404 ms
2025-05-27T14:03:29.044+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:03:29.046+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:03:29.053+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:03:29.054+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T14:03:29.656+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@66547428
2025-05-27T14:03:29.656+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T14:03:29.896+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:03:30.049+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:03:32.194+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:03:32.208+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:03:32.574+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:03:32.594+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:03:32.733+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:03:32.957+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:03:32.957+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:03:32.971+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.433 seconds (process running for 130.172)
2025-05-27T14:03:33.289+02:00  INFO 15520 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:05:06.715+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:05:06.715+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:05:06.716+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T14:05:06.718+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:06.718739400
2025-05-27T14:05:07.977+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:06.718739400 using time unit HOUR
2025-05-27T14:05:07.977+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:05:06.718739400
2025-05-27T14:05:08.043+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:05:08.044+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:08.045+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:08.045+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:08.046+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:05:34.078+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:34.078136800
2025-05-27T14:05:34.141+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:34.078136800 using time unit HOUR
2025-05-27T14:05:34.141+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:05:34.078136800
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:34.219+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:05:34.219+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:34.219047900
2025-05-27T14:05:34.286+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:34.219047900 using time unit DAY
2025-05-27T14:05:34.286+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:05:34.336+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:05:34.423+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:05:34.423+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:38.139+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:38.139703900
2025-05-27T14:05:39.304+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:38.139703900 using time unit DAY
2025-05-27T14:05:39.304+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:05:40.446+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:05:41.394+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:05:41.394+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:41.474+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:41.474+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:05:41.474+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:42.336+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:42.336156300
2025-05-27T14:05:43.246+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:42.336156300 using time unit HOUR
2025-05-27T14:05:43.246+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:05:42.336156300
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:05.925+02:00  INFO 15520 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 47 class path changes (0 additions, 0 deletions, 47 modifications)
2025-05-27T14:06:05.928+02:00  INFO 15520 --- [Thread-8] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:06:06.493+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:06.493722900
2025-05-27T14:06:06.559+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:06.493722900 using time unit HOUR
2025-05-27T14:06:06.560+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:06:06.493722900
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:06.625600500
2025-05-27T14:06:06.691+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:06.625600500 using time unit DAY
2025-05-27T14:06:06.691+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:06.759+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:06:06.760+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:06.833+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:06.833+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:06.906+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:06.906+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:06.906+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:06.907+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:06.910+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:06.910+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:06.910+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:07.359+02:00  INFO 15520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:06:07.361+02:00  INFO 15520 --- [Thread-8] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:07.362+02:00  INFO 15520 --- [Thread-8] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T14:06:07.654+02:00  INFO 15520 --- [Thread-8] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T14:06:07.725+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:06:07.726+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:06:07.937+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:06:07.968+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 10 JPA repository interfaces.
2025-05-27T14:06:08.080+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:06:08.081+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:06:08.081+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:06:08.098+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:06:08.098+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 371 ms
2025-05-27T14:06:08.175+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:06:08.178+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:06:08.184+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:06:08.185+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Starting...
2025-05-27T14:06:08.795+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@33dc9591
2025-05-27T14:06:08.795+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Start completed.
2025-05-27T14:06:09.034+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-3)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:06:09.174+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:06:11.319+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:11.340+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:06:11.725+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:06:11.736+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:06:11.863+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:06:12.100+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:06:12.116+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:06:12.116+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.413 seconds (process running for 289.316)
2025-05-27T14:06:12.480+02:00  INFO 15520 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:06:13.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:06:13.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:06:13.223+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-05-27T14:06:13.223+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:14.115+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:14.115183500
2025-05-27T14:06:14.492+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:14.492+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:14.556+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:14.571+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:15.179+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:15.179418
2025-05-27T14:06:15.357+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:14.115183500 using time unit HOUR
2025-05-27T14:06:15.357+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:06:14.115183500
2025-05-27T14:06:15.416+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:16.024+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:15.179418 using time unit DAY
2025-05-27T14:06:16.024+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:16.091+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:06:17.459+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:18.394+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:18.394+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:18.468+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:18.468+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:18.469+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:18.469+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:18.472+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:18.472+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:18.472+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:42.938+02:00  INFO 15520 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 47 class path changes (0 additions, 0 deletions, 47 modifications)
2025-05-27T14:06:42.939+02:00  INFO 15520 --- [Thread-12] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:06:42.942+02:00  INFO 15520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:06:42.944+02:00  INFO 15520 --- [Thread-12] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:42.945+02:00  INFO 15520 --- [Thread-12] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown initiated...
2025-05-27T14:06:43.237+02:00  INFO 15520 --- [Thread-12] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown completed.
2025-05-27T14:06:43.311+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:06:43.311+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:06:43.522+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:06:43.554+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 10 JPA repository interfaces.
2025-05-27T14:06:43.666+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:06:43.667+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:06:43.667+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:06:43.684+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:06:43.685+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 372 ms
2025-05-27T14:06:43.763+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:06:43.766+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:06:43.773+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:06:43.774+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Starting...
2025-05-27T14:06:44.380+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@cf32300
2025-05-27T14:06:44.380+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Start completed.
2025-05-27T14:06:44.617+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-4)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:06:44.726+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:06:46.852+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:46.884+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:06:47.228+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:06:47.249+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:06:47.375+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:06:47.631+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:06:47.643+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:06:47.645+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.356 seconds (process running for 324.843)
2025-05-27T14:06:47.645+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:06:47.646+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:06:47.646+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-05-27T14:06:47.968+02:00  INFO 15520 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:06:49.948+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:49.948645500
2025-05-27T14:06:50.015+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:49.948645500 using time unit HOUR
2025-05-27T14:06:50.015+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:06:49.948645500
2025-05-27T14:06:50.076+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:50.082973100
2025-05-27T14:06:50.152+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:50.082973100 using time unit DAY
2025-05-27T14:06:50.152+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:50.291+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:50.291+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:50.375+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:50.375+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:50.375+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:59.829+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:59.829272400
2025-05-27T14:07:00.982+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:59.829272400 using time unit DAY
2025-05-27T14:07:00.982+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:07:30.345+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:07:31.580+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:07:31.580+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:07:31.660+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:07:31.660+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:07:31.660+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
