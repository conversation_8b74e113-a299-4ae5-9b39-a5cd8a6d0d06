2025-05-27T13:35:06.029+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 19520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T13:35:06.036+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T13:35:06.076+02:00  INFO 19520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T13:35:06.076+02:00  INFO 19520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T13:35:06.795+02:00  INFO 19520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T13:35:06.878+02:00  INFO 19520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 71 ms. Found 10 JPA repository interfaces.
2025-05-27T13:35:07.438+02:00  INFO 19520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T13:35:07.450+02:00  INFO 19520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T13:35:07.450+02:00  INFO 19520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T13:35:07.490+02:00  INFO 19520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T13:35:07.490+02:00  INFO 19520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1414 ms
2025-05-27T13:35:07.667+02:00  INFO 19520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T13:35:07.729+02:00  INFO 19520 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T13:35:07.760+02:00  INFO 19520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T13:35:08.043+02:00  INFO 19520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T13:35:08.073+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T13:35:08.969+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@27bc26d0
2025-05-27T13:35:08.970+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T13:35:09.261+02:00  INFO 19520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T13:35:10.183+02:00  INFO 19520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T13:35:12.439+02:00  INFO 19520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:35:12.481+02:00  WARN 19520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T13:35:12.702+02:00  INFO 19520 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T13:35:14.346+02:00  WARN 19520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T13:35:14.378+02:00  INFO 19520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T13:35:14.587+02:00  INFO 19520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T13:35:15.036+02:00  INFO 19520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T13:35:15.070+02:00  INFO 19520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T13:35:15.076+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.464 seconds (process running for 10.453)
2025-05-27T13:39:34.805+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T13:39:34.805+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T13:39:34.806+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T13:39:41.685+02:00  WARN 19520 --- [http-nio-8080-exec-7] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T13:39:41.690+02:00  WARN 19520 --- [http-nio-8080-exec-7] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T13:39:48.587+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:39:48.587644700
2025-05-27T13:39:48.661+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:39:48.587644700 using time unit HOUR
2025-05-27T13:39:48.661+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T14:39:48.587644700
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:48.719157200
2025-05-27T13:39:48.798+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:48.719157200 using time unit DAY
2025-05-27T13:39:48.798+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:39:48.877+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:39:48.957+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:39:48.957+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:56.039+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T13:39:56.103+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T13:39:56.103+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:56.168250800
2025-05-27T13:39:56.232+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:56.168250800 using time unit DAY
2025-05-27T13:39:56.232+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:39:56.370+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:39:56.386+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:03.862+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999
2025-05-27T13:40:03.926+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999 using time unit HOUR
2025-05-27T13:40:03.926+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-24T23:00 to 2025-05-26T00:59:59.999999999
2025-05-27T13:40:03.993+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-24T23:00:00.301157 = 1531.142, Last reading: 2025-05-26T00:00:00.206583 = 1536.977
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=2.3669434, 01:00 - 02:00=1.0090332, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.0059814453, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0059814453, 10:00 - 11:00=0.005004883, 11:00 - 12:00=0.005004883, 12:00 - 13:00=0.0059814453, 13:00 - 14:00=0.005004883, 14:00 - 15:00=0.0059814453, 15:00 - 16:00=0.005004883, 16:00 - 17:00=0.0010986328, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.0, 20:00 - 21:00=0.0, 21:00 - 22:00=0.0, 22:00 - 23:00=0.0, 23:00 - 00:00=0.0}
2025-05-27T13:40:03.995+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:40:03.995926800
2025-05-27T13:40:04.062+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:40:03.995926800 using time unit DAY
2025-05-27T13:40:04.063+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:40:04.203+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:40:04.203+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:40:04.266+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:40:04.266+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T13:40:11.903+02:00  INFO 19520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
