plugins {
    id 'java'
    id 'war'
    id 'org.springframework.boot' version '3.5.0'
}

group = 'com.hakcu'
version = '1.0.0'


repositories {
    mavenCentral()
}

dependencies {
    implementation platform('org.springframework.boot:spring-boot-dependencies:3.5.0')

    // https://mvnrepository.com/artifact/com.digitalpetri.modbus/modbus-serial
    implementation 'com.digitalpetri.modbus:modbus-serial:2.1.0'

    // https://mvnrepository.com/artifact/com.itextpdf/itextpdf
    implementation 'com.itextpdf:itextpdf:5.5.13.4'

    // SVG to Image conversion
    // https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-transcoder
    implementation 'org.apache.xmlgraphics:batik-transcoder:1.19'
    // https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-codec
    implementation 'org.apache.xmlgraphics:batik-codec:1.19'

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-devtools'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity6'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'org.postgresql:postgresql'
}

test {
    useJUnitPlatform()
}

bootWar {
    archiveFileName = "PRE.war"
}
