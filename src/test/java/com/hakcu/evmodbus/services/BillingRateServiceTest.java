package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.BillingRate;
import com.hakcu.evmodbus.repositories.BillingRateRepository;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class BillingRateServiceTest {

    @Mock
    private BillingRateRepository billingRateRepository;

    @InjectMocks
    private BillingRateService billingRateService;

    private BillingRate defaultRate;
    private BillingRate nonDefaultRate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Set up test data
        defaultRate = new BillingRate("Default Rate", new BigDecimal("0.15"), new BigDecimal("10.00"));
        defaultRate.setId(1L);
        defaultRate.setDefault(true);

        nonDefaultRate = new BillingRate("Premium Rate", new BigDecimal("0.12"), new BigDecimal("15.00"));
        nonDefaultRate.setId(2L);
        nonDefaultRate.setDefault(false);

        // Set up mocks
        when(billingRateRepository.findAllActive()).thenReturn(List.of(defaultRate, nonDefaultRate));
        when(billingRateRepository.findById(1L)).thenReturn(Optional.of(defaultRate));
        when(billingRateRepository.findById(2L)).thenReturn(Optional.of(nonDefaultRate));
        when(billingRateRepository.findDefaultRate()).thenReturn(Optional.of(defaultRate));
        when(billingRateRepository.save(any(BillingRate.class))).thenAnswer(invocation -> invocation.getArgument(0));
    }

    @Test
    void findAll_ShouldReturnAllActiveRates() {
        // Act
        List<BillingRate> result = billingRateService.findAll();

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.contains(defaultRate));
        assertTrue(result.contains(nonDefaultRate));

        // Verify interactions
        verify(billingRateRepository).findAllActive();
    }

    @Test
    void findById_ShouldReturnRateWhenExists() {
        // Act
        Optional<BillingRate> result = billingRateService.findById(1L);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(defaultRate, result.get());

        // Verify interactions
        verify(billingRateRepository).findById(1L);
    }

    @Test
    void findById_ShouldReturnEmptyWhenNotExists() {
        // Arrange
        when(billingRateRepository.findById(3L)).thenReturn(Optional.empty());

        // Act
        Optional<BillingRate> result = billingRateService.findById(3L);

        // Assert
        assertFalse(result.isPresent());

        // Verify interactions
        verify(billingRateRepository).findById(3L);
    }

    @Test
    void findDefaultRate_ShouldReturnDefaultRate() {
        // Act
        Optional<BillingRate> result = billingRateService.findDefaultRate();

        // Assert
        assertTrue(result.isPresent());
        assertEquals(defaultRate, result.get());
        assertTrue(result.get().isDefault());

        // Verify interactions
        verify(billingRateRepository).findDefaultRate();
    }

    @Test
    void save_ShouldSaveRate() {
        // Arrange
        BillingRate newRate = new BillingRate("New Rate", new BigDecimal("0.10"), new BigDecimal("5.00"));
        newRate.setDefault(false);

        // Act
        BillingRate result = billingRateService.save(newRate);

        // Assert
        assertEquals(newRate, result);

        // Verify interactions
        verify(billingRateRepository).save(newRate);
        verify(billingRateRepository, never()).findDefaultRate(); // Should not check for default rate
    }

    @Test
    void save_ShouldUnsetExistingDefaultWhenNewDefault() {
        // Arrange
        BillingRate newDefaultRate = new BillingRate("New Default", new BigDecimal("0.10"), new BigDecimal("5.00"));
        newDefaultRate.setId(3L);
        newDefaultRate.setDefault(true);

        // Act
        BillingRate result = billingRateService.save(newDefaultRate);

        // Assert
        assertEquals(newDefaultRate, result);
        assertTrue(result.isDefault());

        // Verify interactions
        verify(billingRateRepository).findDefaultRate();
        verify(billingRateRepository).save(defaultRate); // Should save the old default rate with default=false
        verify(billingRateRepository).save(newDefaultRate); // Should save the new default rate
    }

    @Test
    void delete_ShouldMarkRateAsDeleted() {
        // Act
        BillingRate result = billingRateService.delete(1L);

        // Assert
        assertNotNull(result.getDeletedAt());

        // Verify interactions
        verify(billingRateRepository).findById(1L);
        verify(billingRateRepository).save(defaultRate);
    }

    @Test
    void delete_ShouldThrowExceptionWhenRateNotFound() {
        // Arrange
        when(billingRateRepository.findById(3L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(EntityNotFoundException.class, () -> billingRateService.delete(3L));

        // Verify interactions
        verify(billingRateRepository).findById(3L);
        verify(billingRateRepository, never()).save(any());
    }
}
