package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Billing;
import com.hakcu.evmodbus.entities.BillingRate;
import com.hakcu.evmodbus.entities.Customer;
import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.repositories.BillingRepository;
import com.hakcu.evmodbus.repositories.ReadingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BillingBillNumberTest {

    @Mock
    private BillingRepository billingRepository;

    @Mock
    private CustomerService customerService;

    @Mock
    private BillingRateService billingRateService;

    @Mock
    private ReadingService readingService;

    @Mock
    private ReadingRepository readingRepository;

    @InjectMocks
    private BillingService billingService;

    private Customer customer;
    private BillingRate billingRate;
    private YearMonth billingPeriod;
    private List<Spot> spots;

    @BeforeEach
    void setUp() {
        // Set up test data
        customer = new Customer();
        customer.setId(1L);
        customer.setFirstName("John");
        customer.setLastName("Doe");

        billingRate = new BillingRate();
        billingRate.setId(1L);
        billingRate.setName("Standard Rate");
        billingRate.setRatePerKwh(new BigDecimal("0.15"));
        billingRate.setFixedMonthlyFee(new BigDecimal("10.00"));
        billingRate.setDefault(true);

        billingPeriod = YearMonth.now();

        // Create a spot for the customer
        spots = new ArrayList<>();
        Spot spot = new Spot();
        spot.setId(1L);
        spot.setSpotNumber(101);
        spots.add(spot);
        customer.setSpots(spots);

        // Mock repository methods
        when(customerService.findById(anyLong())).thenReturn(Optional.of(customer));
        when(billingRateService.findById(anyLong())).thenReturn(Optional.of(billingRate));
        when(billingRateService.findDefaultRate()).thenReturn(Optional.of(billingRate));
        when(billingRepository.findByPeriodAndCustomerId(any(YearMonth.class), anyLong())).thenReturn(Optional.empty());
        when(readingRepository.calculateTotalConsumptionBySpot(eq(1L), any(LocalDateTime.class), any(LocalDateTime.class))).thenReturn(100.0f);
    }

    @Test
    void getNextBillNumber_ShouldReturnOneWhenNoExistingBills() {
        // Mock repository to return empty for max bill number
        when(billingRepository.findMaxBillNumber()).thenReturn(Optional.empty());

        // Act
        Long nextBillNumber = billingService.getNextBillNumber();

        // Assert
        assertEquals(1L, nextBillNumber);
    }

    @Test
    void getNextBillNumber_ShouldReturnIncrementedValue() {
        // Mock repository to return a max bill number
        when(billingRepository.findMaxBillNumber()).thenReturn(Optional.of(42L));

        // Act
        Long nextBillNumber = billingService.getNextBillNumber();

        // Assert
        assertEquals(43L, nextBillNumber);
    }

    @Test
    void isValidBillNumber_ShouldReturnFalseForExistingNumber() {
        // Mock repository to return true for exists check
        when(billingRepository.existsByBillNumber(anyLong())).thenReturn(true);

        // Act
        boolean isValid = billingService.isValidBillNumber(5L);

        // Assert
        assertFalse(isValid);
    }

    @Test
    void isValidBillNumber_ShouldReturnFalseForNumberLessThanMax() {
        // Mock repository to return false for exists check and a max value
        when(billingRepository.existsByBillNumber(anyLong())).thenReturn(false);
        when(billingRepository.findMaxBillNumber()).thenReturn(Optional.of(10L));

        // Act
        boolean isValid = billingService.isValidBillNumber(5L);

        // Assert
        assertFalse(isValid);
    }

    @Test
    void isValidBillNumber_ShouldReturnTrueForValidNumber() {
        // Mock repository to return false for exists check and a max value
        when(billingRepository.existsByBillNumber(anyLong())).thenReturn(false);
        when(billingRepository.findMaxBillNumber()).thenReturn(Optional.of(10L));

        // Act
        boolean isValid = billingService.isValidBillNumber(15L);

        // Assert
        assertTrue(isValid);
    }

    @Test
    void generateBilling_ShouldAssignNextBillNumber() {
        // Mock repository methods
        when(billingRepository.findMaxBillNumber()).thenReturn(Optional.of(5L));
        when(billingRepository.save(any(Billing.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        Billing billing = billingService.generateBilling(customer.getId(), billingPeriod, billingRate.getId());

        // Assert
        assertNotNull(billing);
        assertEquals(6L, billing.getBillNumber());
    }
}
