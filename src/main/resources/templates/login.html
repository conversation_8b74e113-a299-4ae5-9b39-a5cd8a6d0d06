<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="es">
<head>
    <title>Iniciar sesión</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body class="bg-light">
<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-6 col-lg-5">
            <!-- App info moved from footer to above card -->
            <div class="text-center mb-4">
                <img th:src="@{/android-chrome-192x192.png}" alt="Logo" height="60" class="mb-2">
                <p class="text-muted mb-0 fs-5">EVModbus - Sistema de gestión de cargadores</p>
            </div>
            <div class="card shadow-sm">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        Iniciar sesión
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Alert for error messages -->
                    <div th:if="${param.error}" class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Nombre de usuario o contraseña incorrectos
                    </div>

                    <!-- Alert for logout messages -->
                    <div th:if="${param.logout}" class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        Has cerrado sesión correctamente
                    </div>

                    <!-- Login form -->
                    <form th:action="@{/login}" method="post" id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Nombre de usuario</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person-fill"></i></span>
                                <input type="text" id="username" name="username" class="form-control"
                                       placeholder="Introduce tu nombre de usuario" required autofocus>
                            </div>
                            <div class="form-text">El nombre de usuario no distingue entre mayúsculas y minúsculas</div>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">Contraseña</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                                <input type="password" id="password" name="password" class="form-control"
                                       placeholder="Introduce tu contraseña" required>
                            </div>
                        </div>

                        <div class="d-grid align-items-center">
                            <button type="submit" class="btn btn-primary">
                                Iniciar sesión
                            </button>
                        </div>
                    </form>

                    <!-- Script to convert username to lowercase before form submission -->
                    <script>
                        document.getElementById('loginForm').addEventListener('submit', function(e) {
                            const usernameInput = document.getElementById('username');
                            usernameInput.value = usernameInput.value.toLowerCase();
                        });
                    </script>
                </div>
                <div class="card-footer text-center py-2">
                    <p class="text-muted small mb-0">
                        <i class="bi bi-info-circle me-2"></i>Versión <span class="fw-semibold">1.0.0</span>
                        <span class="mx-2">|</span>
                       EvModbus&nbsp;<i class="bi bi-c-circle me-1"></i><span th:text="${#dates.format(#dates.createNow(), 'yyyy')}"></span>
                        <span class="mx-2">|</span>
                        <i class="bi bi-shield-lock me-2"></i>Acceso seguro
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
