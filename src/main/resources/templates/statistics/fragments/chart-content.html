<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<body>

<!-- Chart Content with Filter Fragment -->
<th:block th:fragment="chart-content-with-filter">
    <!-- Chart Header (tabs) -->
    <div class="card-header py-2 py-md-2">
        <div class="row align-items-center">
            <div class="col-12">
                <ul class="nav nav-tabs nav-fill card-header-tabs flex-column flex-md-row" id="chartTabs" role="tablist">
                    <!-- Hourly Tab Button -->
                    <li class="nav-item flex-fill" role="presentation">
                        <button class="nav-link w-100 d-flex align-items-center justify-content-center" th:id="hourly-tab" th:classappend="${activeTab == 'hourly' ? 'active' : ''}"
                                data-bs-toggle="tab" data-bs-target="#hourly-content"
                                type="button" role="tab" aria-controls="hourly-content" th:aria-selected="${activeTab == 'hourly'}"
                                th:hx-get="@{/statistics/tab-switch(tabId='hourly', spotId=${selectedSpotId}, selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}"
                                hx-target="#chartCard"
                                hx-swap="innerHTML"
                                hx-push-url="false">
                            <div>
                                <i class="bi bi-clock me-2"></i>
                                <span>Consumo por Hora</span>
                                <!-- Desktop view (inline) -->
                                <span class="d-none d-md-inline small text-muted" th:if="${isCurrentDay}">
                                    (hasta <span th:text="${#temporals.format(#temporals.createNow(), 'HH:mm')}"></span>)
                                </span>
                                <!-- Mobile view (block) -->
                                <div class="d-md-none small text-muted" th:if="${isCurrentDay}">
                                    (hasta <span th:text="${#temporals.format(#temporals.createNow(), 'HH:mm')}"></span>)
                                </div>
                            </div>
                        </button>
                    </li>

                    <!-- Daily Tab Button -->
                    <li class="nav-item flex-fill" role="presentation">
                        <button class="nav-link w-100 d-flex align-items-center justify-content-center" th:id="daily-tab" th:classappend="${activeTab == 'daily' ? 'active' : ''}"
                                data-bs-toggle="tab" data-bs-target="#daily-content"
                                type="button" role="tab" aria-controls="daily-content" th:aria-selected="${activeTab == 'daily'}"
                                th:hx-get="@{/statistics/tab-switch(tabId='daily', spotId=${selectedSpotId}, selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}"
                                hx-target="#filter-container"
                                hx-swap="outerHTML"
                                hx-push-url="false">
                            <div>
                                <i class="bi bi-calendar-week me-2"></i>
                                <span>Consumo Diario</span>
                                <!-- Desktop view (inline) -->
                                <span class="d-none d-md-inline small text-muted" th:if="${isCurrentMonth}">
                                    (hasta <span th:text="${#temporals.format(#temporals.createNow(), 'dd/MM')}"></span>)
                                </span>
                                <!-- Mobile view (block) -->
                                <div class="d-md-none small text-muted" th:if="${isCurrentMonth}">
                                    (hasta <span th:text="${#temporals.format(#temporals.createNow(), 'dd/MM')}"></span>)
                                </div>
                            </div>
                        </button>
                    </li>

                    <!-- Monthly Tab Button -->
                    <li class="nav-item flex-fill" role="presentation">
                        <button class="nav-link w-100 d-flex align-items-center justify-content-center" th:id="monthly-tab" th:classappend="${activeTab == 'monthly' ? 'active' : ''}"
                                data-bs-toggle="tab" data-bs-target="#monthly-content"
                                type="button" role="tab" aria-controls="monthly-content" th:aria-selected="${activeTab == 'monthly'}"
                                th:hx-get="@{/statistics/tab-switch(tabId='monthly', spotId=${selectedSpotId}, selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}"
                                hx-target="#filter-container"
                                hx-swap="outerHTML"
                                hx-push-url="false">
                            <div>
                                <i class="bi bi-calendar3 me-2"></i>
                                <span>Consumo Mensual</span>
                                <!-- Desktop view (inline) -->
                                <span class="d-none d-md-inline small text-muted" th:if="${isCurrentYear}">
                                    (hasta <span th:text="${#temporals.format(#temporals.createNow(), 'MMM', new java.util.Locale('es'))}"></span>)
                                </span>
                                <!-- Mobile view (block) -->
                                <div class="d-md-none small text-muted" th:if="${isCurrentYear}">
                                    (hasta <span th:text="${#temporals.format(#temporals.createNow(), 'MMM', new java.util.Locale('es'))}"></span>)
                                </div>
                            </div>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Chart Content Container -->
    <div class="card-body text-center py-0">
        <!-- Tab content container -->
        <div class="tab-content py-0 mb-0" id="chartTabsContent">
            <!-- Hourly Chart Content -->
            <div class="tab-pane fade" th:id="hourly-content" th:classappend="${activeTab == 'hourly' ? 'show active' : ''}"
                 role="tabpanel" aria-labelledby="hourly-tab">
                <!-- Filtered consumption total for hourly tab -->
                <div class="alert alert-info mb-3" th:if="${activeTab == 'hourly' && filteredConsumption != null}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-lightning-charge-fill me-2"></i>Consumo del período seleccionado:</span>
                        <strong th:text="${#numbers.formatDecimal(filteredConsumption, 1, 2) + ' kWh'}">0.00 kWh</strong>
                    </div>
                </div>

                <!-- Show chart if data exists -->
                <div class="chart-container py-0 mb-0" th:if="${hourlyBarChartSvg != null && !hourlyBarChartSvg.isEmpty()}" th:utext="${hourlyBarChartSvg}"></div>

                <!-- Show "No data available" message when no data exists -->
                <div class="chart-container py-0 mb-0 d-flex align-items-center justify-content-center" th:if="${hourlyBarChartSvg == null || hourlyBarChartSvg.isEmpty()}">
                    <div class="text-center p-5">
                        <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No hay datos disponibles</h5>
                        <p class="text-muted">Utilice los controles de navegación para ver otros períodos</p>
                    </div>
                </div>
            </div>

            <!-- Daily Chart Content -->
            <div class="tab-pane fade" th:id="daily-content" th:classappend="${activeTab == 'daily' ? 'show active' : ''}"
                 role="tabpanel" aria-labelledby="daily-tab">
                <!-- Filtered consumption total for daily tab -->
                <div class="alert alert-info mb-3" th:if="${activeTab == 'daily' && filteredConsumption != null}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-lightning-charge-fill me-2"></i>Consumo del período seleccionado:</span>
                        <strong th:text="${#numbers.formatDecimal(filteredConsumption, 1, 2) + ' kWh'}">0.00 kWh</strong>
                    </div>
                </div>

                <!-- Show chart if data exists -->
                <div class="chart-container py-0 mb-0" th:if="${dailyBarChartSvg != null && !dailyBarChartSvg.isEmpty()}" th:utext="${dailyBarChartSvg}"></div>

                <!-- Show "No data available" message when no data exists -->
                <div class="chart-container py-0 mb-0 d-flex align-items-center justify-content-center" th:if="${dailyBarChartSvg == null || dailyBarChartSvg.isEmpty()}">
                    <div class="text-center p-5">
                        <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No hay datos disponibles</h5>
                        <p class="text-muted">Utilice los controles de navegación para ver otros períodos</p>
                    </div>
                </div>
            </div>

            <!-- Monthly Chart Content -->
            <div class="tab-pane fade" th:id="monthly-content" th:classappend="${activeTab == 'monthly' ? 'show active' : ''}"
                 role="tabpanel" aria-labelledby="monthly-tab">
                <!-- Filtered consumption total for monthly tab -->
                <div class="alert alert-info mb-3" th:if="${activeTab == 'monthly' && filteredConsumption != null}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-lightning-charge-fill me-2"></i>Consumo del período seleccionado:</span>
                        <strong th:text="${#numbers.formatDecimal(filteredConsumption, 1, 2) + ' kWh'}">0.00 kWh</strong>
                    </div>
                </div>

                <!-- Show chart if data exists -->
                <div class="chart-container py-0 mb-0" th:if="${monthlyBarChartSvg != null && !monthlyBarChartSvg.isEmpty()}" th:utext="${monthlyBarChartSvg}"></div>

                <!-- Show "No data available" message when no data exists -->
                <div class="chart-container py-0 mb-0 d-flex align-items-center justify-content-center" th:if="${monthlyBarChartSvg == null || monthlyBarChartSvg.isEmpty()}">
                    <div class="text-center p-5">
                        <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No hay datos disponibles</h5>
                        <p class="text-muted">Utilice los controles de navegación para ver otros períodos</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Container -->
    <div class="card-footer py-2">
        <th:block th:replace="~{statistics/fragments/filter-container :: filter-container}"></th:block>
    </div>
</th:block>

</body>
</html>
