<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<body>

<!-- Chart Content with Filter Fragment -->
<th:block th:fragment="chart-content-with-filter">
    <!-- Chart Content Container -->
    <div class="card-body text-center py-0">
        <!-- Tab content container -->
        <div class="tab-content py-0 mb-0" id="chartTabsContent">
            <!-- Hourly Chart Content -->
            <div class="tab-pane fade" th:id="hourly-content" th:classappend="${activeTab == 'hourly' ? 'show active' : ''}"
                 role="tabpanel" aria-labelledby="hourly-tab">
                <!-- Filtered consumption total for hourly tab -->
                <div class="alert alert-info mb-3" th:if="${activeTab == 'hourly' && filteredConsumption != null}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-lightning-charge-fill me-2"></i>Consumo del período seleccionado:</span>
                        <strong th:text="${#numbers.formatDecimal(filteredConsumption, 1, 2) + ' kWh'}">0.00 kWh</strong>
                    </div>
                </div>
                
                <!-- Show chart if data exists -->
                <div class="chart-container py-0 mb-0" th:if="${hourlyBarChartSvg != null && !hourlyBarChartSvg.isEmpty()}" th:utext="${hourlyBarChartSvg}"></div>

                <!-- Show "No data available" message when no data exists -->
                <div class="chart-container py-0 mb-0 d-flex align-items-center justify-content-center" th:if="${hourlyBarChartSvg == null || hourlyBarChartSvg.isEmpty()}">
                    <div class="text-center p-5">
                        <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No hay datos disponibles</h5>
                        <p class="text-muted">Utilice los controles de navegación para ver otros períodos</p>
                    </div>
                </div>
            </div>

            <!-- Daily Chart Content -->
            <div class="tab-pane fade" th:id="daily-content" th:classappend="${activeTab == 'daily' ? 'show active' : ''}"
                 role="tabpanel" aria-labelledby="daily-tab">
                <!-- Filtered consumption total for daily tab -->
                <div class="alert alert-info mb-3" th:if="${activeTab == 'daily' && filteredConsumption != null}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-lightning-charge-fill me-2"></i>Consumo del período seleccionado:</span>
                        <strong th:text="${#numbers.formatDecimal(filteredConsumption, 1, 2) + ' kWh'}">0.00 kWh</strong>
                    </div>
                </div>
                
                <!-- Show chart if data exists -->
                <div class="chart-container py-0 mb-0" th:if="${dailyBarChartSvg != null && !dailyBarChartSvg.isEmpty()}" th:utext="${dailyBarChartSvg}"></div>

                <!-- Show "No data available" message when no data exists -->
                <div class="chart-container py-0 mb-0 d-flex align-items-center justify-content-center" th:if="${dailyBarChartSvg == null || dailyBarChartSvg.isEmpty()}">
                    <div class="text-center p-5">
                        <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No hay datos disponibles</h5>
                        <p class="text-muted">Utilice los controles de navegación para ver otros períodos</p>
                    </div>
                </div>
            </div>

            <!-- Monthly Chart Content -->
            <div class="tab-pane fade" th:id="monthly-content" th:classappend="${activeTab == 'monthly' ? 'show active' : ''}"
                 role="tabpanel" aria-labelledby="monthly-tab">
                <!-- Filtered consumption total for monthly tab -->
                <div class="alert alert-info mb-3" th:if="${activeTab == 'monthly' && filteredConsumption != null}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-lightning-charge-fill me-2"></i>Consumo del período seleccionado:</span>
                        <strong th:text="${#numbers.formatDecimal(filteredConsumption, 1, 2) + ' kWh'}">0.00 kWh</strong>
                    </div>
                </div>
                
                <!-- Show chart if data exists -->
                <div class="chart-container py-0 mb-0" th:if="${monthlyBarChartSvg != null && !monthlyBarChartSvg.isEmpty()}" th:utext="${monthlyBarChartSvg}"></div>

                <!-- Show "No data available" message when no data exists -->
                <div class="chart-container py-0 mb-0 d-flex align-items-center justify-content-center" th:if="${monthlyBarChartSvg == null || monthlyBarChartSvg.isEmpty()}">
                    <div class="text-center p-5">
                        <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No hay datos disponibles</h5>
                        <p class="text-muted">Utilice los controles de navegación para ver otros períodos</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filter Container -->
    <div class="card-footer py-2">
        <th:block th:replace="~{statistics/fragments/filter-container :: filter-container}"></th:block>
    </div>
</th:block>

</body>
</html>
