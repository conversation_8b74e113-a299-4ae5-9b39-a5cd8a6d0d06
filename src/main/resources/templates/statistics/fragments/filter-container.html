<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<body>

<!-- Filter Container Fragment -->
<div class="row w-100" id="filter-container" th:fragment="filter-container">
    <!-- Left side: Filters -->
    <div class="col-12 col-md-8 col-lg-9 mb-3 mb-md-0">
        <!-- Hourly Filter (Day) -->
        <div class="mb-2 mb-md-0 w-100" th:id="hourly-filter" th:style="${activeTab != 'hourly' ? 'display: none;' : ''}">
            <div class="input-group w-100 flex-nowrap">
                <!-- Previous day button -->
                <button class="btn btn-outline-secondary flex-shrink-0" type="button" title="Día anterior"
                        hx-push-url="true" hx-swap="innerHTML" hx-target="#chartCard"
                        th:attr="hx-get=@{/statistics/navigate(spotId=${selectedSpotId}, activeTab='hourly', direction='prev', selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}">
                    <i class="bi bi-chevron-left"></i>
                </button>

                <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                <input class="form-control" hx-include="#selectedDay" hx-push-url="true" hx-swap="innerHTML"
                       hx-target="body"
                       hx-trigger="change"
                       id="selectedDay"
                       name="selectedDay"
                       th:attr="hx-get=@{/statistics(spotId=${selectedSpotId}, activeTab='hourly', selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}"
                       th:value="${selectedDay}"
                       type="date">

                <!-- Next day button -->
                <button class="btn btn-outline-secondary flex-shrink-0" type="button" title="Día siguiente"
                        hx-push-url="true" hx-swap="innerHTML" hx-target="#chartCard"
                        th:attr="hx-get=@{/statistics/navigate(spotId=${selectedSpotId}, activeTab='hourly', direction='next', selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}">
                    <i class="bi bi-chevron-right"></i>
                </button>
            </div>
        </div>

        <!-- Daily Filter (Month) -->
        <div class="mb-2 mb-md-0 w-100" th:id="daily-filter" th:style="${activeTab != 'daily' ? 'display: none;' : ''}">
            <div class="input-group w-100 flex-nowrap">
                <!-- Previous month button -->
                <button class="btn btn-outline-secondary flex-shrink-0" type="button" title="Mes anterior"
                        hx-push-url="true" hx-swap="innerHTML" hx-target="#chartCard"
                        th:attr="hx-get=@{/statistics/navigate(spotId=${selectedSpotId}, activeTab='daily', direction='prev', selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}">
                    <i class="bi bi-chevron-left"></i>
                </button>

                <span class="input-group-text"><i class="bi bi-calendar-month"></i></span>
                <input class="form-control" hx-include="#selectedMonth" hx-push-url="true" hx-swap="innerHTML"
                       hx-target="body"
                       hx-trigger="change"
                       id="selectedMonth"
                       name="selectedMonth"
                       th:attr="hx-get=@{/statistics(spotId=${selectedSpotId}, activeTab='daily', selectedDay=${selectedDay}, selectedYear=${selectedYear})}"
                       th:value="${selectedMonth}"
                       type="month">

                <!-- Next month button -->
                <button class="btn btn-outline-secondary flex-shrink-0" type="button" title="Mes siguiente"
                        hx-push-url="true" hx-swap="innerHTML" hx-target="#chartCard"
                        th:attr="hx-get=@{/statistics/navigate(spotId=${selectedSpotId}, activeTab='daily', direction='next', selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}">
                    <i class="bi bi-chevron-right"></i>
                </button>
            </div>
        </div>

        <!-- Monthly Filter (Year) -->
        <div class="mb-2 mb-md-0 w-100" th:id="monthly-filter" th:style="${activeTab != 'monthly' ? 'display: none;' : ''}">
            <div class="input-group w-100 flex-nowrap">
                <!-- Previous year button -->
                <button class="btn btn-outline-secondary flex-shrink-0" type="button" title="Año anterior"
                        hx-push-url="true" hx-swap="innerHTML" hx-target="#chartCard"
                        th:attr="hx-get=@{/statistics/navigate(spotId=${selectedSpotId}, activeTab='monthly', direction='prev', selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}">
                    <i class="bi bi-chevron-left"></i>
                </button>

                <span class="input-group-text"><i class="bi bi-calendar3-event"></i></span>
                <input class="form-control" hx-include="#selectedYear" hx-push-url="true" hx-swap="innerHTML"
                       hx-target="body"
                       hx-trigger="change"
                       id="selectedYear"
                       name="selectedYear"
                       th:attr="hx-get=@{/statistics(spotId=${selectedSpotId}, activeTab='monthly', selectedDay=${selectedDay}, selectedMonth=${selectedMonth})}"
                       th:value="${selectedYear}"
                       type="number">

                <!-- Next year button -->
                <button class="btn btn-outline-secondary flex-shrink-0" type="button" title="Año siguiente"
                        hx-push-url="true" hx-swap="innerHTML" hx-target="#chartCard"
                        th:attr="hx-get=@{/statistics/navigate(spotId=${selectedSpotId}, activeTab='monthly', direction='next', selectedDay=${selectedDay}, selectedMonth=${selectedMonth}, selectedYear=${selectedYear})}">
                    <i class="bi bi-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Right side: Download buttons -->
    <div class="col-12 col-md-4 col-lg-3 text-center text-md-end" id="download-buttons">
        <!-- Hourly Download Button -->
        <a class="btn btn-outline-primary mb-2 mb-md-0 w-100" th:id="hourly-download" th:style="${activeTab != 'hourly' ? 'display: none;' : ''}"
           th:href="@{/statistics/pdf(spotId=${selectedSpotId}, reportType='day', selectedDay=${selectedDay})}"
           target="_blank" title="Descargar PDF Diario">
            <i class="bi bi-file-earmark-pdf-fill text-danger me-1"></i> <span>Informe Diario</span>
        </a>

        <!-- Daily Download Button -->
        <a class="btn btn-outline-primary mb-2 mb-md-0 w-100" th:id="daily-download" th:style="${activeTab != 'daily' ? 'display: none;' : ''}"
           th:href="@{/statistics/pdf(spotId=${selectedSpotId}, reportType='month', selectedMonth=${selectedMonth})}"
           target="_blank" title="Descargar PDF Mensual">
            <i class="bi bi-file-earmark-pdf-fill text-danger me-1"></i> <span>Informe Mensual</span>
        </a>

        <!-- Monthly Download Button -->
        <a class="btn btn-outline-primary mb-2 mb-md-0 w-100" th:id="monthly-download" th:style="${activeTab != 'monthly' ? 'display: none;' : ''}"
           th:href="@{/statistics/pdf(spotId=${selectedSpotId}, reportType='year', selectedYear=${selectedYear})}"
           target="_blank" title="Descargar PDF Anual">
            <i class="bi bi-file-earmark-pdf-fill text-danger me-1"></i> <span>Informe Anual</span>
        </a>
    </div>
</div>

</body>
</html>
