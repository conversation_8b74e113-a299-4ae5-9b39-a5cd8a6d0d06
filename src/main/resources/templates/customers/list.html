<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>Lista de usuarios</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Usuarios')}"></th:block>
<th:block th:replace="~{layout/navbar :: floating-action('customers')}"></th:block>
<div class="container-sm mb-5">

    <!-- Modal -->
    <div aria-hidden="true" aria-labelledby="customerModalLabel" class="modal fade" id="customerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div id="modalContent">
                    <!-- Form content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Search Input Outside Card -->
    <form th:action="@{/customers}" method="get" id="filterForm" class="mb-4">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" id="filterText" name="filterText" class="form-control" th:value="${filterText}"
                   placeholder="Buscar por nombre, apellido, teléfono, email, dirección, etc."
                   th:attr="hx-get=@{/customers}"
                   hx-target="body"
                   hx-swap="innerHTML"
                   hx-push-url="true"
                   hx-include="#filterForm"
                   hx-trigger="keyup changed delay:500ms">
            <a th:if="${filterText != null && !filterText.isEmpty()}" th:href="@{/customers}" class="btn btn-outline-secondary">
                <i class="bi bi-x-circle"></i> Limpiar
            </a>
        </div>
    </form>

    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        <div class="col" th:each="customer : ${customers}">
            <div class="card shadow-sm w-100 h-100 d-flex flex-column">
                <div class="card-header d-flex justify-content-between pb-1 pt-1">
                    <h5 class="card-title d-flex align-items-center mb-0">
                        <i class="bi bi-person-fill me-2"></i>
                        <span th:text="${customer.lastName + ', ' + customer.firstName}"></span>
                    </h5>
                    <div class="d-flex align-items-center p-0">
                        <button class="btn btn-sm btn-link p-0 me-2"
                                data-bs-target="#customerModal"
                                data-bs-toggle="modal"
                                hx-target="#modalContent"
                                sec:authorize="hasAnyRole('ADMIN', 'MANAGER')"
                                th:attr="hx-get=@{/customers/edit/{id}(id=${customer.id})}">
                            <i class="bi bi-pencil-square fs-5"></i>
                        </button>
                        <a class="btn btn-sm btn-link p-0"
                           hx-confirm="¿Desea eliminar este usuario?"
                           hx-swap="delete"
                           hx-target="closest .col"
                           sec:authorize="hasAnyRole('ADMIN')"
                           th:attr="hx-delete=@{/customers/{id}(id=${customer.id})}">
                            <i class="bi bi-trash3 text-danger fs-5"></i>
                        </a>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-person-badge text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Nombre:</span>
                                        <span th:text="${customer.firstName != null && !customer.firstName.isEmpty() ? customer.firstName : '---'}"></span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-person-vcard text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Apellidos:</span>
                                        <span th:text="${customer.lastName != null && !customer.lastName.isEmpty() ? customer.lastName : '---'}"></span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center w-100">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-telephone-fill text-primary me-2"></i>
                                </div>
                                <div class="d-flex flex-column">
                                    <span class="text-muted small">Teléfono:</span>
                                    <span th:text="${customer.mainPhoneNumber != null && !customer.mainPhoneNumber.isEmpty() ? customer.mainPhoneNumber : '---'}"></span>
                                </div>
                                <div class="ms-auto align-self-center">
                                    <a th:if="${customer.mainPhoneNumber != null && !customer.mainPhoneNumber.isEmpty()}"
                                       th:href="${'tel:' + customer.mainPhoneNumber}"
                                       class="btn btn-sm btn-outline-primary rounded-circle d-flex align-items-center justify-content-center"
                                       style="width: 28px; height: 28px;">
                                        <i class="bi bi-telephone"></i>
                                    </a>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center w-100">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-telephone-plus-fill text-primary me-2"></i>
                                </div>
                                <div class="d-flex flex-column">
                                    <span class="text-muted small">Teléfono adicional:</span>
                                    <span th:text="${customer.secondPhoneNumber != null && !customer.secondPhoneNumber.isEmpty() ? customer.secondPhoneNumber : '---'}"></span>
                                </div>
                                <div class="ms-auto align-self-center">
                                    <a th:if="${customer.secondPhoneNumber != null && !customer.secondPhoneNumber.isEmpty()}"
                                       th:href="${'tel:' + customer.secondPhoneNumber}"
                                       class="btn btn-sm btn-outline-primary rounded-circle d-flex align-items-center justify-content-center"
                                       style="width: 28px; height: 28px;">
                                        <i class="bi bi-telephone"></i>
                                    </a>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center w-100">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-envelope-fill text-primary me-2"></i>
                                </div>
                                <div class="d-flex flex-column">
                                    <span class="text-muted small">Email:</span>
                                    <span th:text="${customer.mainEmailAddress != null && !customer.mainEmailAddress.isEmpty() ? customer.mainEmailAddress : '---'}"></span>
                                </div>
                                <div class="ms-auto align-self-center">
                                    <a th:if="${customer.mainEmailAddress != null && !customer.mainEmailAddress.isEmpty()}"
                                       th:href="${'mailto:' + customer.mainEmailAddress}"
                                       class="btn btn-sm btn-outline-primary rounded-circle d-flex align-items-center justify-content-center"
                                       style="width: 28px; height: 28px;">
                                        <i class="bi bi-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center w-100">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-envelope-plus-fill text-primary me-2"></i>
                                </div>
                                <div class="d-flex flex-column">
                                    <span class="text-muted small">Email adicional:</span>
                                    <span th:text="${customer.secondEmailAddress != null && !customer.secondEmailAddress.isEmpty() ? customer.secondEmailAddress : '---'}"></span>
                                </div>
                                <div class="ms-auto align-self-center">
                                    <a th:if="${customer.secondEmailAddress != null && !customer.secondEmailAddress.isEmpty()}"
                                       th:href="${'mailto:' + customer.secondEmailAddress}"
                                       class="btn btn-sm btn-outline-primary rounded-circle d-flex align-items-center justify-content-center"
                                       style="width: 28px; height: 28px;">
                                        <i class="bi bi-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Dirección:</span>
                                        <span th:text="${customer.address != null && !customer.address.isEmpty() ? customer.address : '---'}"></span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2 d-flex">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-mailbox text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Código postal:</span>
                                        <span th:text="${customer.postalCode != null && !customer.postalCode.isEmpty() ? customer.postalCode : '---'}"></span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2 d-flex">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-building text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Ciudad:</span>
                                        <span th:text="${customer.city != null && !customer.city.isEmpty() ? customer.city : '---'}"></span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-card-text text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">DNI:</span>
                                        <span th:text="${customer.dni != null && !customer.dni.isEmpty() ? customer.dni : '---'}"></span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item py-2 d-flex">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-bank2 text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">IBAN:</span>
                                        <span th:text="${customer.iban != null && !customer.iban.isEmpty() ? customer.iban : '---'}"></span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="card-footer border-top">
                    <!-- Message for customers with no spots -->
                    <div class="d-flex flex-column py-1" th:if="${customer.spots == null || customer.spots.isEmpty()}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi bi-p-square text-muted me-1"></i>
                                <span class="text-muted">Sin plazas asignadas</span>
                            </div>
                        </div>
                        <div class="mt-1 d-flex justify-content-between align-items-center">
                            <small class="text-muted">Estado:</small>
                            <span class="badge bg-secondary rounded-pill">
                                <i class="bi bi-dash-circle me-1"></i>
                                <span>No disponible</span>
                            </span>
                        </div>
                    </div>

                    <!-- Spots with readings for customers with spots -->
                    <div th:if="${customer.spots != null && !customer.spots.isEmpty()}">
                        <div class="d-flex flex-column py-1"
                             th:each="spot, spotStat : ${customer.spots}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-p-square-fill text-primary me-1"></i>
                                    <span th:text="${'Plaza ' + spot.spotNumber + ' Sótano ' + spot.floor.floorNumber}"></span>
                                </div>
                            </div>
                            <div class="mt-1 d-flex justify-content-between align-items-center">
                                <small class="text-muted">Última lectura:</small>
                                <span class="badge bg-success rounded-pill"
                                      th:if="${spotReadings.get(spot.id).isPresent() && spotReadings.get(spot.id).get().success}">
                                    <i class="bi bi-lightning-charge-fill me-1"></i>
                                    <span th:text="${spotReadings.get(spot.id).get().reading + ' kW/h'}"></span>
                                </span>
                                <span class="badge bg-secondary rounded-pill"
                                      th:if="${!spotReadings.get(spot.id).isPresent() || !spotReadings.get(spot.id).get().success}">
                                    <i class="bi bi-dash-circle me-1"></i>
                                    <span>Sin lectura</span>
                                </span>
                            </div>
                            <hr th:if="${!spotStat.last}" class="my-2">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Include toast notifications -->
<th:block th:replace="~{layout/toast-include :: toast-include}"></th:block>
</body>
</html>
