<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="es">
<head>
    <title>Editar factura</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Editar factura')}"></th:block>
<div class="container-sm mb-5">
    <!-- Alert messages -->
    <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${successMessage}">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span th:text="${successMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${errorMessage}">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span th:text="${errorMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Draft status notice -->
    <div class="alert alert-info mb-4">
        <i class="bi bi-info-circle-fill me-2"></i>
        <strong>Está editando una factura en estado borrador.</strong> Puede modificar el número de factura, la tarifa aplicada y añadir descuentos.
        Una vez finalizada, no se podrán realizar más cambios.
    </div>

    <!-- Main form container -->
    <form th:action="@{/billing/{id}/edit(id=${billing.id})}" method="post"
          th:attr="hx-post=@{/billing/{id}/edit(id=${billing.id})},
                   hx-target='body',
                   hx-swap='outerHTML',
                   hx-disabled-elt='#saveBtn, #cancelBtn, #billNumber, #billingRateId, #discountAmount, #comments'">
        <div class="modal-form-content">
            <!-- Read-only Information Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        Información de la factura
                    </h5>
                    <div class="btn-group">
                        <a th:href="@{/billing/{id}(id=${billing.id})}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Volver
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-flex flex-column">
                                <span class="text-muted small">Cliente</span>
                                <span class="fs-6 fw-medium" th:text="${billing.customer.firstName + ' ' + billing.customer.lastName}"></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex flex-column">
                                <span class="text-muted small">Estado</span>
                                <span class="fs-6 fw-medium">
                                    <span class="badge bg-secondary">Borrador</span>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex flex-column">
                                <span class="text-muted small">Período</span>
                                <span class="fs-6 fw-medium" th:text="${#temporals.format(billing.billingPeriod, 'MMMM yyyy', new java.util.Locale('es', 'ES'))}"></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex flex-column">
                                <span class="text-muted small">Fecha de emisión</span>
                                <span class="fs-6 fw-medium" th:text="${#temporals.format(billing.issueDate, 'dd/MM/yyyy')}"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Editable Fields Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pencil-square me-2"></i>
                        Editar detalles de la factura
                    </h5>
                </div>
                <div class="card-body position-relative">
                    <div class="p-2 p-md-3 border rounded-3 bg-light mb-4">
                        <!-- Billing Details Section -->
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-file-earmark-text me-2"></i>Información básica
                        </h6>
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="billNumber" class="form-label">Número de factura</label>
                                <input type="number" class="form-control" id="billNumber" name="billNumber" required
                                       min="1" th:value="${billing.billNumber}">
                                <div class="form-text">Debe ser único y mayor que 0.</div>
                            </div>
                            <div class="col-md-6">
                                <label for="billingRateId" class="form-label">Tarifa aplicada</label>
                                <select class="form-select" id="billingRateId" name="billingRateId" required>
                                    <option value="" disabled>Seleccione una tarifa</option>
                                    <option th:each="rate : ${billingRates}"
                                            th:value="${rate.id}"
                                            th:text="${rate.name}"
                                            th:selected="${billing.billingRate != null && billing.billingRate.id == rate.id}"></option>
                                </select>
                                <div class="form-text">Cambiar la tarifa recalculará todos los ítems de consumo.</div>
                            </div>
                        </div>
                    </div>

                    <div class="p-2 p-md-3 border rounded-3 bg-light mb-4">
                        <!-- Financial Information Section -->
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-currency-euro me-2"></i>Información financiera
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="discountAmount" class="form-label">Descuento (€)</label>
                                <div class="input-group mb-3">
                                    <input type="number" class="form-control" id="discountAmount" name="discountAmount"
                                           min="0" step="0.01" th:value="${billing.discountAmount != null ? billing.discountAmount : 0}">
                                    <span class="input-group-text">€</span>
                                </div>
                                <div class="form-text">Descuento aplicado al subtotal antes de impuestos.</div>
                            </div>
                        </div>
                    </div>

                    <div class="p-2 p-md-3 border rounded-3 bg-light mb-4">
                        <!-- Comments Section -->
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-chat-left-text me-2"></i>Notas adicionales
                        </h6>
                        <div class="row">
                            <div class="col-12">
                                <label for="comments" class="form-label">Comentarios</label>
                                <textarea class="form-control" id="comments" name="comments" rows="4"
                                    placeholder="Añada aquí cualquier comentario o nota relevante para esta factura"
                                    th:text="${billing.comments != null && !#strings.isEmpty(#strings.trim(billing.comments)) ? billing.comments : ''}"></textarea>
                                <div class="form-text">Estos comentarios se mostrarán en la factura PDF si se incluyen.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a th:href="@{/billing/{id}(id=${billing.id})}" class="btn btn-outline-secondary" id="cancelBtn">Cancelar</a>
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="bi bi-save me-2"></i>Guardar cambios
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <!-- CSS Styles -->
    <style>
        /* When the form is being submitted, fade form content */
        form.htmx-request .modal-form-content {
            opacity: 0.5;
            pointer-events: none;
        }

        /* Disable buttons during request */
        .htmx-request #saveBtn,
        .htmx-request #cancelBtn {
            pointer-events: none;
        }

        /* Styling for the editable sections */
        .card-body .border.rounded-3 {
            transition: all 0.2s ease-in-out;
        }

        .card-body .border.rounded-3:hover {
            box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
        }
    </style>

    <!-- JavaScript for form handling -->
    <script>
        // Handle form submission
        document.addEventListener('htmx:beforeRequest', function(event) {
            if (event.detail.elt.id === 'saveBtn' || event.detail.elt.closest('form')) {
                // The htmx-request class will be added to the form automatically
                // and our CSS will handle showing/hiding elements
                console.log('Form submission started');

                // Disable the save button
                document.getElementById('saveBtn').setAttribute('disabled', 'disabled');
                document.getElementById('saveBtn').classList.add('disabled');

                // Disable the cancel button
                document.getElementById('cancelBtn').setAttribute('disabled', 'disabled');
                document.getElementById('cancelBtn').classList.add('disabled');
            }
        });

        // Handle request completion
        document.addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.elt.id === 'saveBtn' || event.detail.elt.closest('form')) {
                if (event.detail.failed) {
                    // If the request failed, show the form content again
                    document.querySelector('.modal-form-content').style.opacity = '1';
                    document.querySelector('.modal-form-content').style.pointerEvents = 'auto';

                    // Re-enable the save button
                    document.getElementById('saveBtn').removeAttribute('disabled');
                    document.getElementById('saveBtn').classList.remove('disabled');

                    // Re-enable the cancel button
                    document.getElementById('cancelBtn').removeAttribute('disabled');
                    document.getElementById('cancelBtn').classList.remove('disabled');

                    console.log('Form submission failed');
                } else {
                    console.log('Form submission completed successfully');
                }
            }
        });

        // Handle request after swap (successful completion)
        document.addEventListener('htmx:afterSwap', function(event) {
            console.log('Content swapped successfully');
        });
    </script>

</body>
</html>
