<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="es">
<head>
    <title>Detalle de factura</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Detalle de factura')}"></th:block>
<div class="container-sm mb-5">
    <!-- Alert messages -->
    <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${successMessage}">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span th:text="${successMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${errorMessage}">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span th:text="${errorMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Draft status notice -->
    <div class="alert alert-info mb-4" th:if="${billing.status.name() == 'DRAFT'}">
        <i class="bi bi-info-circle-fill me-2"></i>
        <strong>Esta factura está en estado borrador.</strong> Puede editar los ítems de consumo y luego finalizar el borrador para convertirla en una factura pendiente.
        Una vez finalizada, no se podrán realizar más cambios en los ítems.
    </div>

    <!-- Billing header card -->
    <div class="card shadow-sm mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-receipt me-2"></i>
                Factura #<span th:text="${billing.billNumber}"></span>
            </h5>
            <div class="btn-group">
                <a th:href="@{/billing}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Volver
                </a>
                <a th:if="${billing.status.name() == 'DRAFT'}" th:href="@{/billing/{id}/edit(id=${billing.id})}" class="btn btn-outline-primary">
                    <i class="bi bi-pencil-square me-2"></i>Editar
                </a>
                <a th:if="${billing.status.name() != 'DRAFT'}" th:href="@{/billing/export/pdf/{id}(id=${billing.id})}" hx-boost="false" class="btn btn-outline-danger">
                    <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Billing information -->
                <div class="col-md-6">
                    <h6 class="fw-bold">Información de facturación</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>Número de factura:</th>
                            <td th:text="${billing.billNumber}"></td>
                        </tr>
                        <tr>
                            <th>Período:</th>
                            <td th:text="${#temporals.format(billing.billingPeriod, 'MMMM yyyy', new java.util.Locale('es', 'ES'))}"></td>
                        </tr>
                        <tr>
                            <th>Fecha de emisión:</th>
                            <td th:text="${#temporals.format(billing.issueDate, 'dd/MM/yyyy')}"></td>
                        </tr>
                        <tr>
                            <th>Fecha de vencimiento:</th>
                            <td th:text="${#temporals.format(billing.dueDate, 'dd/MM/yyyy')}"></td>
                        </tr>
                        <tr>
                            <th>Estado:</th>
                            <td>
                                <span class="badge rounded-pill text-bg-success" th:if="${billing.status.name() == 'PAID'}">Pagada</span>
                                <span class="badge rounded-pill text-bg-warning" th:if="${billing.status.name() == 'PENDING'}">Pendiente</span>
                                <span class="badge rounded-pill text-bg-secondary" th:if="${billing.status.name() == 'DRAFT'}">Borrador</span>
                            </td>
                        </tr>
                        <tr th:if="${billing.paid && billing.paidDate != null}">
                            <th>Fecha de pago:</th>
                            <td th:text="${#temporals.format(billing.paidDate, 'dd/MM/yyyy')}"></td>
                        </tr>
                        <tr>
                            <th>Tarifa aplicada:</th>
                            <td>
                                <span th:if="${billing.billingRate != null}" th:text="${billing.billingRate.name}"></span>
                                <span th:if="${billing.billingRate == null}" class="text-muted">Sin tarifa</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Customer information -->
                <div class="col-md-6">
                    <h6 class="fw-bold">Información del cliente</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>Nombre:</th>
                            <td th:text="${billing.customer.firstName + ' ' + billing.customer.lastName}"></td>
                        </tr>
                        <tr th:if="${billing.customer.dni != null && !billing.customer.dni.isEmpty()}">
                            <th>DNI:</th>
                            <td th:text="${billing.customer.dni}"></td>
                        </tr>
                        <tr th:if="${billing.customer.address != null && !billing.customer.address.isEmpty()}">
                            <th>Dirección:</th>
                            <td th:text="${billing.customer.fullAddress}"></td>
                        </tr>
                        <tr th:if="${billing.customer.mainEmailAddress != null && !billing.customer.mainEmailAddress.isEmpty()}">
                            <th>Email:</th>
                            <td th:text="${billing.customer.mainEmailAddress}"></td>
                        </tr>
                        <tr th:if="${billing.customer.mainPhoneNumber != null && !billing.customer.mainPhoneNumber.isEmpty()}">
                            <th>Teléfono:</th>
                            <td th:text="${billing.customer.mainPhoneNumber}"></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="card-footer d-flex justify-content-between align-items-center">
            <div>
                <span class="fw-bold">Consumo total:</span>
                <span th:text="${#numbers.formatDecimal(billing.totalConsumption, 1, 2) + ' kWh'}"></span>
            </div>
            <div>
                <!-- Draft status actions -->
                <form th:if="${billing.status.name() == 'DRAFT'}" th:action="@{/billing/{id}/finalize(id=${billing.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>Finalizar borrador
                    </button>
                </form>

                <!-- Pending status actions -->
                <form th:if="${billing.status.name() == 'PENDING'}" th:action="@{/billing/{id}/mark-paid(id=${billing.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-2"></i>Marcar como pagada
                    </button>
                </form>

                <!-- Paid status actions -->
                <form th:if="${billing.status.name() == 'PAID'}" th:action="@{/billing/{id}/mark-unpaid(id=${billing.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-x-circle me-2"></i>Marcar como no pagada
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Billing items card -->
    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Detalle de consumo
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Plaza</th>
                            <th>Consumo (kWh)</th>
                            <th>Tarifa (€/kWh)</th>
                            <th>Importe (€)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item : ${billing.items}">
                            <td th:text="'Plaza ' + ${item.spot.spotNumber} + ' - Sótano ' + ${item.spot.floor.floorNumber}"></td>
                            <td th:text="${#numbers.formatDecimal(item.consumption, 1, 2)}"></td>
                            <td th:text="${#numbers.formatDecimal(item.rateApplied, 1, 2) + ' €'}"></td>
                            <td th:text="${#numbers.formatDecimal(item.amount, 1, 2) + ' €'}"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Comments card - only shown if comments exist -->
    <div class="card shadow-sm mb-4" th:if="${billing.comments != null && !billing.comments.trim().isEmpty()}">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-chat-left-text me-2"></i>
                Comentarios
            </h5>
        </div>
        <div class="card-body">
            <p class="mb-0" th:text="${billing.comments}"></p>
        </div>
    </div>

    <!-- Billing summary card -->
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-calculator me-2"></i>
                Resumen de facturación
            </h5>
        </div>
        <div class="card-body">
            <div class="row justify-content-end">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <th>Cuota fija:</th>
                            <td class="text-end" th:text="${#numbers.formatDecimal(billing.fixedFeeAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr>
                            <th>Consumo:</th>
                            <td class="text-end" th:text="${#numbers.formatDecimal(billing.consumptionAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr>
                            <th>Subtotal:</th>
                            <td class="text-end" th:with="subtotal=${billing.fixedFeeAmount.add(billing.consumptionAmount)}"
                                th:text="${#numbers.formatDecimal(subtotal, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr th:if="${billing.discountAmount != null && billing.discountAmount.compareTo(T(java.math.BigDecimal).ZERO) > 0}">
                            <th>Descuento:</th>
                            <td class="text-end text-danger" th:text="${'-' + #numbers.formatDecimal(billing.discountAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr th:if="${billing.discountAmount != null && billing.discountAmount.compareTo(T(java.math.BigDecimal).ZERO) > 0}">
                            <th>Subtotal con descuento:</th>
                            <td class="text-end" th:with="subtotalWithDiscount=${billing.fixedFeeAmount.add(billing.consumptionAmount).subtract(billing.discountAmount)}"
                                th:text="${#numbers.formatDecimal(subtotalWithDiscount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr>
                            <th>IVA (21%):</th>
                            <td class="text-end" th:text="${#numbers.formatDecimal(billing.taxAmount, 1, 2) + ' €'}"></td>
                        </tr>
                        <tr class="table-active">
                            <th class="fs-5">TOTAL:</th>
                            <td class="text-end fs-5 fw-bold" th:text="${#numbers.formatDecimal(billing.totalAmount, 1, 2) + ' €'}"></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Script to ensure proper scrolling behavior -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Remove any Bootstrap modal classes that might be left on the body
        document.body.classList.remove('modal-open');

        // Remove any inline styles that might be affecting scrolling
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('padding-right');

        // Force the body to be scrollable
        document.body.style.overflow = 'auto';

        // Remove any modal backdrops that might still be present
        const modalBackdrops = document.querySelectorAll('.modal-backdrop');
        modalBackdrops.forEach(backdrop => {
            backdrop.remove();
        });
    });
</script>
</body>
</html>
