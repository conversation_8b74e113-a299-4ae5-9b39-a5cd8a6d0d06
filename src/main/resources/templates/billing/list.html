<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="es">
<head>
    <title>Facturación</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
    <style>
        /* Modal loading state styles - initially completely hidden */
        #loadingIndicator {
            display: none;
            height: 0;
            overflow: hidden;
            margin: 0;
            padding: 0;
            visibility: hidden;
        }

        /* When the form is being submitted, hide all form content */
        .modal-body.htmx-request .modal-form-content,
        .modal-body.is-loading .modal-form-content {
            display: none !important;
        }

        /* When the form is being submitted, show the loading indicator */
        .modal-body.htmx-request #loadingIndicator,
        .modal-body.is-loading #loadingIndicator {
            display: block !important;
            height: auto !important;
            min-height: 300px !important;
            overflow: visible !important;
            margin: 0 !important;
            padding: 0 !important;
            visibility: visible !important;
        }
    </style>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Facturación')}"></th:block>
<th:block th:replace="~{layout/navbar :: floating-action('billing')}"></th:block>
<div class="container-sm mb-5">
    <!-- Alert messages -->
    <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${successMessage}">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span th:text="${successMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${errorMessage}">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span th:text="${errorMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- No billing rates warning -->
    <div class="alert alert-warning" role="alert" th:if="${!billingRatesExist}">
        <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-triangle-fill me-2 fs-4"></i>
            <div>
                <h5 class="alert-heading mb-1">No se pueden generar facturas</h5>
                <p class="mb-0">No existen tarifas de facturación en el sistema. Debe crear al menos una tarifa antes de poder generar facturas.</p>
                <button type="button" class="btn btn-warning btn-sm mt-2"
                        data-bs-toggle="modal"
                        data-bs-target="#billingRateModal"
                        th:attr="hx-get=@{/billing/rates/modal/new}"
                        hx-target="#modalContent"
                        hx-trigger="click">
                    <i class="bi bi-plus-circle me-2"></i>Crear nueva tarifa
                </button>
            </div>
        </div>
    </div>



    <!-- Filter card -->
    <div class="card shadow-sm mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel-fill me-2"></i>
                Filtros
            </h5>
            <div>
                <a th:href="@{/billing}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-x-circle me-2"></i>Limpiar filtros
                </a>
            </div>
        </div>
        <div class="card-body">
            <form th:action="@{/billing}" method="get" id="filterForm" class="row g-3">
                <!-- Bill Number filter -->
                <div class="col-md-3">
                    <label for="billNumber" class="form-label">Nº Factura</label>
                    <div class="input-group">
                        <input type="number" id="billNumber" name="billNumber" class="form-control" th:value="${billNumber}"
                               placeholder="Buscar por número"
                               th:attr="hx-get=@{/billing}"
                               hx-target="body"
                               hx-swap="innerHTML"
                               hx-push-url="true"
                               hx-include="#filterForm"
                               hx-trigger="keyup changed delay:500ms">
                        <a th:if="${billNumber != null}" th:href="@{/billing(customerId=${customerId},period=${period},paid=${paid})}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    </div>
                </div>

                <!-- Customer filter -->
                <div class="col-md-3">
                    <label for="customerId" class="form-label">Cliente</label>
                    <select class="form-select" id="customerId" name="customerId"
                           th:attr="hx-get=@{/billing}"
                           hx-target="body"
                           hx-swap="innerHTML"
                           hx-push-url="true"
                           hx-include="#filterForm"
                           hx-trigger="change">
                        <option value="">Todos los clientes</option>
                        <option th:each="customer : ${customers}"
                                th:value="${customer.id}"
                                th:text="${customer.firstName + ' ' + customer.lastName}"
                                th:selected="${customerId != null && customerId == customer.id}"></option>
                    </select>
                </div>

                <!-- Period filter -->
                <div class="col-md-3">
                    <label for="period" class="form-label">Período</label>
                    <input type="month" class="form-control" id="period" name="period" th:value="${period}"
                           th:attr="hx-get=@{/billing}"
                           hx-target="body"
                           hx-swap="innerHTML"
                           hx-push-url="true"
                           hx-include="#filterForm"
                           hx-trigger="change">
                </div>

                <!-- Payment status filter -->
                <div class="col-md-3">
                    <label for="paid" class="form-label">Estado</label>
                    <select class="form-select" id="paid" name="paid"
                           th:attr="hx-get=@{/billing}"
                           hx-target="body"
                           hx-swap="innerHTML"
                           hx-push-url="true"
                           hx-include="#filterForm"
                           hx-trigger="change">
                        <option value="">Todos</option>
                        <option value="true" th:selected="${paid != null && paid == true}">Pagadas</option>
                        <option value="false" th:selected="${paid != null && paid == false}">Pendientes/Borradores</option>
                    </select>
                </div>

                <!-- Filter buttons section removed and moved to header -->
            </form>
        </div>
    </div>

    <!-- Billing list - Always visible -->
    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-receipt me-2"></i>
                Facturas
            </h5>
            <div class="btn-group">
                <!-- Generate bill button - disabled when no billing rates exist -->
                <button type="button" class="btn btn-outline-primary btn-sm"
                        th:classappend="${!billingRatesExist ? 'disabled' : ''}"
                        th:data-bs-toggle="${billingRatesExist ? 'modal' : ''}"
                        th:data-bs-target="${billingRatesExist ? '#generateBillingModal' : ''}"
                        th:title="${!billingRatesExist ? 'No se pueden generar facturas porque no existen tarifas de facturación' : 'Generar nueva factura'}">
                    <i class="bi bi-plus-circle me-2"></i>Generar factura
                </button>
                <a th:href="@{/billing/rates}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-gear-fill me-2"></i>Gestionar tarifas
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <!-- No results message inside card body -->
            <div class="alert alert-info m-3" th:if="${billings.isEmpty()}">
                <i class="bi bi-info-circle-fill me-2"></i>
                No se encontraron facturas que coincidan con los filtros seleccionados.
            </div>

            <!-- Table only shown when there are bills -->
            <div class="table-responsive" th:if="${!billings.isEmpty()}">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Nº Factura</th>
                            <th>Cliente</th>
                            <th>Período</th>
                            <th>Fecha emisión</th>
                            <th>Tarifa</th>
                            <th>Importe</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="billing : ${billings}">
                            <td th:text="${billing.billNumber}"></td>
                            <td th:text="${billing.customer.firstName + ' ' + billing.customer.lastName}"></td>
                            <td th:text="${#temporals.format(billing.billingPeriod, 'MMMM yyyy', new java.util.Locale('es', 'ES'))}"></td>
                            <td th:text="${#temporals.format(billing.issueDate, 'dd/MM/yyyy')}"></td>
                            <td>
                                <span th:if="${billing.billingRate != null}" th:text="${billing.billingRate.name}"></span>
                                <span th:if="${billing.billingRate == null}" class="text-muted">Sin tarifa</span>
                            </td>
                            <td th:text="${#numbers.formatDecimal(billing.totalAmount, 1, 2) + ' €'}"></td>
                            <td>
                                <span class="badge rounded-pill text-bg-success" th:if="${billing.status.name() == 'PAID'}">Pagada</span>
                                <span class="badge rounded-pill text-bg-warning" th:if="${billing.status.name() == 'PENDING'}">Pendiente</span>
                                <span class="badge rounded-pill text-bg-secondary" th:if="${billing.status.name() == 'DRAFT'}">Borrador</span>
                            </td>
                            <td>
                                <div class="d-flex gap-2">
                                    <a class="btn btn-sm btn-link p-0"
                                       th:href="@{/billing/{id}(id=${billing.id})}" title="Ver detalles">
                                        <i class="bi bi-eye fs-5 text-primary"></i>
                                    </a>
                                    <a class="btn btn-sm btn-link p-0"
                                       hx-boost="false"
                                       th:if="${billing.status.name() != 'DRAFT'}"
                                       th:href="@{/billing/export/pdf/{id}(id=${billing.id})}" title="Exportar PDF">
                                        <i class="bi bi-file-earmark-pdf fs-5 text-danger"></i>
                                    </a>
                                    <span class="btn btn-sm btn-link p-0 disabled"
                                          th:if="${billing.status.name() == 'DRAFT'}"
                                          title="No disponible para borradores">
                                        <i class="bi bi-file-earmark-pdf fs-5 text-muted"></i>
                                    </span>
                                    <button class="btn btn-sm btn-link p-0"
                                            sec:authorize="hasRole('ADMIN')"
                                            th:attr="hx-delete=@{/billing/{id}(id=${billing.id})}, hx-confirm='¿Está seguro de que desea eliminar esta factura?', hx-swap='delete', hx-target='closest tr'">
                                        <i class="bi bi-trash3 fs-5 text-danger"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- Card footer with pagination - only show when pagination is needed and there are bills -->
        <div class="card-footer text-center py-2" th:if="${totalPages > 0 && !billings.isEmpty()}">
            <div class="d-flex justify-content-center align-items-center gap-3">
                <!-- First page -->
                <a class="text-decoration-none" th:classappend="${currentPage == 0} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == 0} ? '#' : @{/billing(page=0, size=${pageSize}, customerId=${customerId}, period=${period}, paid=${paid}, billNumber=${billNumber})}"
                   style="cursor: pointer;">
                    <i class="bi bi-chevron-double-left fs-5"></i>
                </a>

                <!-- Previous page -->
                <a class="text-decoration-none" th:classappend="${currentPage == 0} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == 0} ? '#' : @{/billing(page=${currentPage - 1}, size=${pageSize}, customerId=${customerId}, period=${period}, paid=${paid}, billNumber=${billNumber})}"
                   style="cursor: pointer;">
                    <i class="bi bi-chevron-left fs-5"></i>
                </a>

                <!-- Page numbers -->
                <div class="d-flex gap-2 align-items-center">
                    <a th:each="i : ${#numbers.sequence(startPage != null ? startPage : 0, endPage != null ? endPage : totalPages - 1)}"
                       class="text-decoration-none rounded-circle d-inline-flex justify-content-center align-items-center"
                       style="width: 30px; height: 30px; cursor: pointer;"
                       th:classappend="${i == currentPage} ? 'bg-primary text-white fw-bold' : 'text-primary'"
                       th:href="@{/billing(page=${i}, size=${pageSize}, customerId=${customerId}, period=${period}, paid=${paid}, billNumber=${billNumber})}"
                       th:text="${i + 1}">1</a>
                </div>

                <!-- Next page -->
                <a class="text-decoration-none" th:classappend="${currentPage == totalPages - 1} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == totalPages - 1} ? '#' : @{/billing(page=${currentPage + 1}, size=${pageSize}, customerId=${customerId}, period=${period}, paid=${paid}, billNumber=${billNumber})}"
                   style="cursor: pointer;">
                    <i class="bi bi-chevron-right fs-5"></i>
                </a>

                <!-- Last page -->
                <a class="text-decoration-none" th:classappend="${currentPage == totalPages - 1} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == totalPages - 1} ? '#' : @{/billing(page=${totalPages - 1}, size=${pageSize}, customerId=${customerId}, period=${period}, paid=${paid}, billNumber=${billNumber})}"
                   style="cursor: pointer;">
                    <i class="bi bi-chevron-double-right fs-5"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Billing Rate Modal -->
<div class="modal fade" id="billingRateModal" tabindex="-1" aria-labelledby="billingRateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div id="modalContent">
                <!-- Form content will be loaded here via HTMX -->
            </div>
        </div>
    </div>
</div>

<!-- Generate Billing Modal -->
<div class="modal fade" id="generateBillingModal" tabindex="-1" aria-labelledby="generateBillingModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="generateBillingModalLabel">Generar factura</h5>
                <button type="button" class="btn-close" id="modalCloseBtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body position-relative">
                <!-- Loading indicator that will be shown during form submission -->
                <div id="loadingIndicator" class="htmx-indicator" aria-hidden="true" style="display: none; position: absolute; top: 0; left: 0; right: 0; z-index: 10;">
                    <div th:replace="~{billing/fragments/loading :: loading}"></div>
                </div>

                <!-- Form content that will be hidden during submission -->
                <div class="modal-form-content">
                    <!-- Warning when no billing rates exist -->
                    <div class="alert alert-warning" th:if="${!billingRatesExist}">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        No existen tarifas de facturación en el sistema. Debe crear al menos una tarifa antes de poder generar facturas.
                        <div class="mt-2">
                            <button type="button" class="btn btn-warning btn-sm"
                                    data-bs-toggle="modal"
                                    data-bs-target="#billingRateModal"
                                    th:attr="hx-get=@{/billing/rates/modal/new}"
                                    hx-target="#modalContent"
                                    hx-trigger="click">
                                <i class="bi bi-plus-circle me-2"></i>Crear nueva tarifa
                            </button>
                        </div>
                    </div>

                    <form th:action="@{/billing/generate-unified}" method="post" id="unifiedBillingForm" class="mt-3"
                          th:attr="hx-post=@{/billing/generate-unified},
                                   hx-target='body',
                                   hx-swap='outerHTML',
                                   hx-indicator='#loadingIndicator',
                                   hx-disabled-elt='#generateBtn, #cancelBtn, #modalCloseBtn, #modalCustomerId, #modalPeriod, #modalBillingRateId, #modalBillNumber'"
                          onsubmit="showLoadingState()">

                    <!-- Hidden input to store billingRatesExist value for JavaScript -->
                    <input type="hidden" id="billingRatesExist" th:value="${billingRatesExist}">

                    <!-- Form is disabled when no billing rates exist -->
                    <fieldset th:disabled="${!billingRatesExist}">
                        <div class="mb-3">
                            <label for="modalCustomerId" class="form-label">Cliente</label>
                            <select class="form-select" id="modalCustomerId" name="customerId" required
                                    th:attr="hx-get=@{/billing/toggle-warning}, hx-target='#allCustomersWarning', hx-trigger='change', hx-swap='outerHTML'">
                                <option value="all" selected>Todos los clientes</option>
                                <option th:each="customer : ${customers}"
                                        th:value="${customer.id}"
                                        th:text="${customer.firstName + ' ' + customer.lastName}"></option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="modalPeriod" class="form-label">Período</label>
                            <input type="month" class="form-control" id="modalPeriod" name="period" required
                                   th:value="${#temporals.format(lastValidBillingPeriod, 'yyyy-MM')}"
                                   th:max="${#temporals.format(lastValidBillingPeriod, 'yyyy-MM')}"
                                   onchange="validateBillingPeriod(this)">
                            <div class="form-text">Solo se pueden generar facturas para meses completos (anteriores al mes actual).</div>
                            <div id="invalidPeriodWarning" class="alert alert-danger mt-2 d-none">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                No se pueden generar facturas para el mes actual o meses futuros.
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="modalBillingRateId" class="form-label">Tarifa de facturación</label>
                            <select class="form-select" id="modalBillingRateId" name="billingRateId" required>
                                <option th:each="rate : ${billingRates}"
                                        th:value="${rate.id}"
                                        th:text="${rate.name + ' (' + #numbers.formatDecimal(rate.ratePerKwh, 1, 2) + ' €/kWh, ' + #numbers.formatDecimal(rate.fixedMonthlyFee, 1, 2) + ' € cuota fija)' + (rate.default ? ' [Predeterminada]' : '')}"
                                        th:selected="${rate.default}"></option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="modalBillNumber" class="form-label">Número de factura</label>
                            <div class="input-group">
                                <input type="number" class="form-control w-25" id="modalBillNumber" name="billNumber"
                                       th:value="${nextBillNumber}"
                                       th:min="${nextBillNumber}"
                                       min="1"
                                       oninvalid="this.setCustomValidity('El número de factura debe ser mayor o igual a ' + this.min)"
                                       oninput="this.setCustomValidity('')">
                                <span class="input-group-text w-75">Siguiente disponible:&nbsp;<span th:text="${nextBillNumber}"></span></span>
                            </div>
                            <div class="form-text">Deje este campo vacío para usar el número automático, o introduzca un número personalizado mayor que el último utilizado. Para generación por lotes, este será el número inicial y se incrementará secuencialmente.</div>
                        </div>
                        <div th:replace="~{billing/fragments/warning :: warning(showWarning=true)}"></div>
                    </fieldset>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelBtn" data-bs-dismiss="modal">Cancelar</button>
                <button type="submit" class="btn btn-primary" id="generateBtn" form="unifiedBillingForm" th:disabled="${!billingRatesExist}">Generar</button>
            </div>

            <script>
                function showLoadingState() {
                    // Show loading indicator
                    const loadingIndicator = document.getElementById('loadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'block';
                        loadingIndicator.style.visibility = 'visible';
                        loadingIndicator.style.height = 'auto';
                    }

                    // Hide form content
                    const formContent = document.querySelector('.modal-form-content');
                    if (formContent) {
                        formContent.style.display = 'none';
                    }

                    // Add a class to the modal body to indicate loading state
                    const modalBody = document.querySelector('.modal-body');
                    if (modalBody) {
                        modalBody.classList.add('is-loading');
                    }

                    // Disable all buttons and close options
                    document.getElementById('generateBtn').disabled = true;
                    document.getElementById('cancelBtn').disabled = true;
                    document.getElementById('modalCloseBtn').disabled = true;

                    // Add warning message about not closing the window
                    const modalTitle = document.querySelector('.modal-title');
                    if (modalTitle) {
                        modalTitle.innerHTML = '<i class="bi bi-hourglass-split me-2 text-warning"></i>Generando factura - No cierre esta ventana';
                    }

                    // Register a listener for the response
                    document.addEventListener('htmx:beforeSwap', function(event) {
                        // Ensure the modal is properly closed before page transition
                        const modal = bootstrap.Modal.getInstance(document.getElementById('generateBillingModal'));
                        if (modal) {
                            modal.hide();
                        }

                        // Clean up modal artifacts
                        document.body.classList.remove('modal-open');
                        document.body.style.removeProperty('overflow');
                        document.body.style.removeProperty('padding-right');

                        // Remove any modal backdrops
                        const modalBackdrops = document.querySelectorAll('.modal-backdrop');
                        modalBackdrops.forEach(backdrop => {
                            backdrop.remove();
                        });
                    }, { once: true }); // Only run this once

                    // Don't prevent the form from submitting
                    return true;
                }
            </script>
        </div>
    </div>
</div>


    <!-- Script to handle loading state in the modal and validate billing period -->
    <script>
        // Function to validate billing period
        function validateBillingPeriod(input) {
            const selectedPeriod = input.value;
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

            // Format current month as YYYY-MM for comparison
            const currentPeriod = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;

            // Get the warning element
            const warningElement = document.getElementById('invalidPeriodWarning');

            // Get the generate button
            const generateBtn = document.getElementById('generateBtn');

            // Check if selected period is current month or future
            if (selectedPeriod >= currentPeriod) {
                // Show warning and disable button
                warningElement.classList.remove('d-none');
                if (generateBtn) {
                    generateBtn.disabled = true;
                }
                input.setCustomValidity('No se pueden generar facturas para el mes actual o meses futuros');
            } else {
                // Hide warning and enable button if billing rates exist
                warningElement.classList.add('d-none');
                if (generateBtn && document.getElementById('billingRatesExist')) {
                    generateBtn.disabled = false;
                }
                input.setCustomValidity('');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Get the form and loading indicator elements
            const billingForm = document.getElementById('unifiedBillingForm');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const generateBillingModal = document.getElementById('generateBillingModal');
            const generateBtn = document.getElementById('generateBtn');

            // Validate period field on initial load
            const periodInput = document.getElementById('modalPeriod');
            if (periodInput) {
                validateBillingPeriod(periodInput);
            }

            // Ensure loading indicator is hidden when modal opens
            if (generateBillingModal) {
                generateBillingModal.addEventListener('show.bs.modal', function() {
                    // Make sure loading indicator is hidden when modal opens
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'none';
                    }

                    // Remove loading class from modal body if present
                    const modalBody = document.querySelector('.modal-body');
                    if (modalBody) {
                        modalBody.classList.remove('is-loading');
                    }

                    // Enable all buttons
                    if (generateBtn) {
                        // Only enable if period is valid
                        const periodInput = document.getElementById('modalPeriod');
                        if (periodInput) {
                            validateBillingPeriod(periodInput);
                        } else {
                            generateBtn.disabled = false;
                        }
                    }

                    // Enable close button
                    const closeBtn = document.getElementById('modalCloseBtn');
                    if (closeBtn) {
                        closeBtn.disabled = false;
                    }

                    // Reset modal title
                    const modalTitle = document.querySelector('.modal-title');
                    if (modalTitle) {
                        modalTitle.innerHTML = 'Generar factura';
                    }

                    // Show form content if it was hidden
                    const formContent = document.querySelector('.modal-form-content');
                    if (formContent) {
                        formContent.style.display = 'block';
                    }
                });
            }
        });
    </script>
</body>
</html>
