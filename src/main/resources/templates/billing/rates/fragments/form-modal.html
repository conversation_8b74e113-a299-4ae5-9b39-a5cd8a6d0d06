<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <!-- Billing Rate Form Modal Fragment -->
    <div th:fragment="form-modal(billingRate)" class="modal-body" style="padding: 0">
        <form id="billingRateForm" method="post" th:action="@{/billing/rates}"
              th:object="${billingRate}"
              th:attr="hx-post=@{/billing/rates},
                       hx-target='body',
                       hx-swap='outerHTML',
                       hx-push-url='true'">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-tag-fill me-2"></i>
                        <span th:text="${billingRate.id == null} ? 'Nueva tarifa' : 'Editar tarifa'"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="card-body">
                    <!-- Hidden ID field for updates -->
                    <input type="hidden" th:field="*{id}" />
                    
                    <div class="row g-3">
                        <!-- Name -->
                        <div class="col-md-6">
                            <label for="name" class="form-label">Nombre</label>
                            <input type="text" class="form-control" id="name" th:field="*{name}" required>
                        </div>
                        
                        <!-- Is Default -->
                        <div class="col-md-6">
                            <label class="form-label">Tarifa predeterminada</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isDefault" th:field="*{default}">
                                <label class="form-check-label" for="isDefault">
                                    Establecer como tarifa predeterminada
                                </label>
                            </div>
                        </div>
                        
                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">Descripción</label>
                            <textarea class="form-control" id="description" th:field="*{description}" rows="2"></textarea>
                        </div>
                        
                        <!-- Rate per kWh -->
                        <div class="col-md-6">
                            <label for="ratePerKwh" class="form-label">Tarifa por kWh (€)</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="ratePerKwh" th:field="*{ratePerKwh}" 
                                       step="0.0001" min="0" required>
                                <span class="input-group-text">€/kWh</span>
                            </div>
                        </div>
                        
                        <!-- Fixed monthly fee -->
                        <div class="col-md-6">
                            <label for="fixedMonthlyFee" class="form-label">Cuota fija mensual (€)</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="fixedMonthlyFee" th:field="*{fixedMonthlyFee}" 
                                       step="0.01" min="0" required>
                                <span class="input-group-text">€</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3" th:if="*{default}">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        Esta tarifa se aplicará a los clientes que no tengan una tarifa específica asignada.
                    </div>
                    
                    <div class="alert alert-warning mt-3" th:if="*{id != null && default}">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Si desmarca esta tarifa como predeterminada, deberá establecer otra tarifa como predeterminada.
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </div>
        </form>
    </div>
</body>
</html>
