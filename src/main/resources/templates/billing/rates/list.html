<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="es">
<head>
    <title>Tarifas de facturación</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Tarifas de facturación')}"></th:block>
<!-- No floating action button needed as we have our own -->

<!-- Modal for adding/editing billing rates -->
<div class="modal fade" id="billingRateModal" tabindex="-1" aria-labelledby="billingRateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div id="modalContent">
                <!-- Form content will be loaded here via HTMX -->
            </div>
        </div>
    </div>
</div>

<!-- Custom floating action button that opens the modal -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <button class="btn btn-success btn-lg rounded-circle shadow"
            data-bs-target="#billingRateModal"
            data-bs-toggle="modal"
            hx-target="#modalContent"
            hx-trigger="click"
            th:attr="hx-get=@{/billing/rates/modal/new}">
        <i class="bi bi-plus-circle"></i>
    </button>
</div>

<div class="container-sm mb-5">
    <!-- Alert messages -->
    <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${successMessage}">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span th:text="${successMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${errorMessage}">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span th:text="${errorMessage}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Action buttons -->
    <div class="d-flex justify-content-between mb-4">
        <a th:href="@{/billing}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Volver a facturación
        </a>
        <button type="button" class="btn btn-primary"
                data-bs-toggle="modal"
                data-bs-target="#billingRateModal"
                th:attr="hx-get=@{/billing/rates/modal/new}"
                hx-target="#modalContent"
                hx-trigger="click">
            <i class="bi bi-plus-circle me-2"></i>Nueva tarifa
        </button>
    </div>

    <!-- No rates message -->
    <div class="alert alert-info" th:if="${rates.isEmpty()}">
        <i class="bi bi-info-circle-fill me-2"></i>
        No hay tarifas de facturación configuradas. Cree una nueva tarifa para comenzar.
    </div>

    <!-- Rates list -->
    <div class="card shadow-sm" th:if="${!rates.isEmpty()}">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-tag-fill me-2"></i>
                Tarifas
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Nombre</th>
                            <th>Descripción</th>
                            <th>Tarifa (€/kWh)</th>
                            <th>Cuota fija (€)</th>
                            <th>Predeterminada</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="rate : ${rates}">
                            <td th:text="${rate.name}"></td>
                            <td th:text="${rate.description}"></td>
                            <td th:text="${#numbers.formatDecimal(rate.ratePerKwh, 1, 2) + ' €'}"></td>
                            <td th:text="${#numbers.formatDecimal(rate.fixedMonthlyFee, 1, 2) + ' €'}"></td>
                            <td>
                                <span class="badge rounded-pill text-bg-primary" th:if="${rate.default}">Predeterminada</span>
                            </td>
                            <td>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-link p-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#billingRateModal"
                                            th:attr="hx-get=@{/billing/rates/modal/edit/{id}(id=${rate.id})}"
                                            hx-target="#modalContent"
                                            hx-trigger="click"
                                            title="Editar tarifa">
                                        <i class="bi bi-pencil-square fs-5 text-primary"></i>
                                    </button>
                                    <button class="btn btn-sm btn-link p-0"
                                            th:attr="onclick='deleteRate(' + ${rate.id} + ')'">
                                        <i class="bi bi-trash3 fs-5 text-danger"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for rate operations -->
<script>
    // Function to delete a rate
    function deleteRate(id) {
        if (confirm('¿Está seguro de que desea eliminar esta tarifa?')) {
            fetch('/billing/rates/' + id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                if (response.ok) {
                    window.location.reload();
                } else {
                    alert('Error al eliminar la tarifa');
                }
            });
        }
    }
</script>
</body>
</html>
