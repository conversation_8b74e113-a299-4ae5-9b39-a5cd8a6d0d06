<nav th:fragment="navbar(pageTitle)" class="navbar navbar-expand-lg navbar-dark bg-dark mb-4" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
  <div class="container-fluid">
    <!-- Brand and page title area -->
    <div class="d-flex align-items-center">
      <!-- Dynamic page title from parameter -->
      <span id="page-title" class="text-light mb-0 d-lg-block fs-2" th:text="${pageTitle}"></span>
    </div>

    <!-- Mobile-friendly navbar toggler -->
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>

    <!-- Collapsible navigation content -->
    <div class="collapse navbar-collapse" id="navbarContent">
      <ul class="navbar-nav ms-auto">
        <!-- Only show these items when authenticated -->
        <li class="nav-item" sec:authorize="isAuthenticated()">
          <a class="nav-link" th:href="@{/status}"><i class="bi bi-speedometer2 me-2"></i>Estado</a>
        </li>
        <li class="nav-item" sec:authorize="isAuthenticated()">
          <a class="nav-link" th:href="@{/spots}"><i class="bi bi-p-square-fill me-2"></i>Plazas</a>
        </li>
        <li class="nav-item" sec:authorize="hasRole('ADMIN')">
          <a class="nav-link" th:href="@{/recipients}"><i class="bi bi-envelope-fill me-2"></i>Destinatarios</a>
        </li>
        <li class="nav-item" sec:authorize="isAuthenticated()">
          <a class="nav-link" th:href="@{/customers}"><i class="bi bi-person-fill me-2"></i>Usuarios</a>
        </li>
        <li class="nav-item" sec:authorize="isAuthenticated()">
          <a class="nav-link" th:href="@{/readings}"><i class="bi bi-clock-history me-2"></i>Lecturas</a>
        </li>
        <li class="nav-item" sec:authorize="isAuthenticated()">
          <a class="nav-link" th:href="@{/statistics}"><i class="bi bi-bar-chart-fill me-2"></i>Estadísticas</a>
        </li>

        <!-- Billing link (only for ADMIN and MANAGER roles) -->
        <li class="nav-item" sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">
          <a class="nav-link" th:href="@{/billing}"><i class="bi bi-receipt me-2"></i>Facturación</a>
        </li>

        <!-- Admin panel link (only for ADMIN role) -->
        <li class="nav-item" sec:authorize="hasRole('ADMIN')">
          <a class="nav-link" th:href="@{/admin}"><i class="bi bi-gear-fill me-2"></i>Administración</a>
        </li>

        <!-- User dropdown with logout option -->
        <li class="nav-item dropdown" sec:authorize="isAuthenticated()">
          <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="bi bi-person-circle me-2"></i>
            <span sec:authentication="name">Usuario</span>
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
            <li>
              <form th:action="@{/logout}" method="post" class="dropdown-item">
                <button type="submit" class="btn btn-link text-decoration-none p-0">
                  <i class="bi bi-box-arrow-right me-2"></i>Cerrar sesión
                </button>
              </form>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</nav>

<!-- Floating action button container -->
<div th:fragment="floating-action(buttonType)" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
  <!-- Spots create button -->
  <button th:if="${buttonType == 'spots'}"
          class="btn btn-success btn-lg rounded-circle shadow"
          data-bs-target="#spotModal"
          data-bs-toggle="modal"
          hx-target="#modalContent"
          hx-trigger="click"
          th:attr="hx-get=@{/spots/new}">
    <i class="bi bi-plus-circle"></i>
  </button>

  <!-- Customers create button -->
  <button th:if="${buttonType == 'customers'}"
          class="btn btn-success btn-lg rounded-circle shadow"
          data-bs-target="#customerModal"
          data-bs-toggle="modal"
          hx-target="#modalContent"
          hx-trigger="click"
          th:attr="hx-get=@{/customers/new}">
    <i class="bi bi-person-plus-fill"></i>
  </button>

  <!-- Recipients create button -->
  <button th:if="${buttonType == 'recipients'}"
          class="btn btn-success btn-lg rounded-circle shadow"
          data-bs-target="#recipientModal"
          data-bs-toggle="modal"
          hx-target="#modalContent"
          hx-trigger="click"
          th:attr="hx-get=@{/recipients/new}">
    <i class="bi bi-envelope-plus-fill"></i>
  </button>

  <!-- Billing rates button removed - now handled directly in the rates list page -->

  <!-- Floors create button -->
  <button th:if="${buttonType == 'floors'}"
          class="btn btn-success btn-lg rounded-circle shadow"
          th:attr="hx-get=@{/floors/new}">
    <i class="bi bi-plus-circle"></i>
  </button>
</div>
