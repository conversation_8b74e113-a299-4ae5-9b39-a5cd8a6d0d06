<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="es">
<head th:fragment="header">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Favicon links -->
    <link rel="apple-touch-icon" sizes="180x180" th:href="@{/apple-touch-icon.png}">
    <link rel="icon" type="image/png" sizes="32x32" th:href="@{/favicon-32x32.png}">
    <link rel="icon" type="image/png" sizes="16x16" th:href="@{/favicon-16x16.png}">
    <link rel="icon" type="image/x-icon" th:href="@{/favicon.ico}">
    <link rel="manifest" th:href="@{/site.webmanifest}">
    <!-- CSS and JS links -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.0/font/bootstrap-icons.min.css">
    <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>

    <!-- Global script to ensure proper page transitions and fix scrolling issues -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fix scrolling issues by removing Bootstrap modal artifacts
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('overflow');
            document.body.style.removeProperty('padding-right');

            // Remove any modal backdrops that might be present
            const modalBackdrops = document.querySelectorAll('.modal-backdrop');
            modalBackdrops.forEach(backdrop => {
                backdrop.remove();
            });

            // Listen for HTMX page transitions
            document.body.addEventListener('htmx:afterSwap', function() {
                // Fix scrolling issues after page transitions
                document.body.classList.remove('modal-open');
                document.body.style.removeProperty('overflow');
                document.body.style.removeProperty('padding-right');
                document.body.style.overflow = 'auto';

                // Remove any modal backdrops
                const modalBackdrops = document.querySelectorAll('.modal-backdrop');
                modalBackdrops.forEach(backdrop => {
                    backdrop.remove();
                });
            });
        });
    </script>
    <title></title>
</head>
</html>