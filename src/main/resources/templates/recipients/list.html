<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Lista de destinatarios</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Destinatarios')}"></th:block>
<th:block th:replace="~{layout/navbar :: floating-action('recipients')}"></th:block>
<div class="container-sm mb-5">

    <!-- Modal -->
    <div aria-hidden="true" aria-labelledby="recipientModalLabel" class="modal fade" id="recipientModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div id="modalContent">
                    <!-- Form content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Replace with a single card containing a list -->
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-envelope-fill me-2"></i>
                Correos electrónicos
            </h5>
        </div>
        <div class="card-body p-0">
            <ul class="list-group list-group-flush">
                <!-- Empty state message when no recipients exist -->
                <li class="list-group-item text-center text-muted" th:if="${recipients.isEmpty()}">
                    <i class="bi bi-envelope-dash me-2"></i>
                    No hay destinatarios configurados
                </li>
                <!-- List of recipients -->
                <li class="list-group-item" th:each="recipient : ${recipients}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span th:text="${recipient.email}"></span>
                        <div>
                            <button class="btn btn-sm btn-link p-0"
                                    data-bs-target="#recipientModal"
                                    data-bs-toggle="modal"
                                    hx-target="#modalContent"
                                    th:attr="hx-get=@{/recipients/edit/{id}(id=${recipient.id})}">
                                <i class="bi bi-pencil-square fs-5"></i>
                            </button>
                            <a class="btn btn-sm btn-link p-0"
                               hx-confirm="¿Desea eliminar este destinatario?"
                               hx-swap="delete"
                               hx-target="closest li"
                               th:attr="hx-delete=@{/recipients/{id}(id=${recipient.id})}">
                                <i class="bi bi-trash3 text-danger fs-5"></i>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="card-footer">
            <small class="text-muted">
                <i class="bi bi-info-circle me-1"></i>
                Estos correos electrónicos recibirán alertas cuando todas las lecturas de una planta fallen, indicando un posible problema con la instalación eléctrica.
            </small>
        </div>
    </div>
</div>

<!-- Include toast notifications -->
<th:block th:replace="~{layout/toast-include :: toast-include}"></th:block>
</body>
</html>
