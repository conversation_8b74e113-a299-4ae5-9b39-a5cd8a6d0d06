<!DOCTYPE html>
<html lang="es" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Lista de plazas de aparcamiento</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Plazas')}"></th:block>
<th:block th:replace="~{layout/navbar :: floating-action('spots')}"></th:block>
<div class="container-sm mb-5">
    <!-- Modal -->
    <div aria-hidden="true" aria-labelledby="spotModalLabel" class="modal fade" id="spotModal" tabindex="-1">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div id="modalContent">
                    <!-- Form content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        <div class="col d-flex" th:each="spot : ${spots}">
            <div class="card shadow-sm w-100 d-flex flex-column h-100">
                <div class="card-header d-flex justify-content-between pb-1 pt-1">
                    <h5 class="card-title d-flex align-items-center mb-0">
                        <i class="bi bi-car-front-fill me-2"></i>
                        <span th:text="'Plaza '+${spot.spotNumber}+' - Sótano '+${spot.floor.floorNumber}"></span>
                    </h5>
                    <div class="d-flex align-items-center p-0">
                        <a class="btn btn-sm btn-link p-0"
                           hx-confirm="¿Desea eliminar esta plaza?"
                           hx-swap="delete"
                           hx-target="closest .col"
                           sec:authorize="hasRole('ADMIN')"
                           th:attr="hx-delete=@{/spots/{id}(id=${spot.id})}">
                            <i class="bi bi-trash3 text-danger fs-5"></i>
                        </a>
                    </div>
                </div>
                <div class="flex-grow-1 h-100">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-person-fill text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Usuario:</span>
                                        <!-- Display for regular USERS (read-only) -->
                                        <div sec:authorize="hasRole('USER')">
                                            <span th:if="${spot.customer != null}"
                                                  th:text="${spot.customer.firstName + ' ' + spot.customer.lastName}">
                                            </span>
                                            <span class="text-muted" th:if="${spot.customer == null}">
                                            Sin usuario
                                            </span>
                                        </div>
                                        <div sec:authorize="hasAnyRole('MANAGER', 'ADMIN')">
                                            <form class="w-100"
                                                  hx-swap="none"
                                                  hx-trigger="change delay:500ms"
                                                  th:attr="hx-post=@{/spots/assign-customer}">
                                                <select class="form-select" id="customerSelect" name="customerId"
                                                        th:disabled="${not (#authorization.expression('hasAnyRole(''ADMIN'') or hasRole(''MANAGER'')'))}">>
                                                    <option value="">Sin usuario</option>
                                                    <option th:each="customer : ${customers}"
                                                            th:selected="${spot.customer != null && spot.customer.id == customer.id}"
                                                            th:text="${customer.firstName + ' ' + customer.lastName}"
                                                            th:value="${customer.id}">
                                                    </option>
                                                </select>
                                                <input name="spotId" th:value="${spot.id}" type="hidden">
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <!-- Consumo -->
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-lightning-charge-fill text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Consumo:</span>
                                        <span hx-on:htmx:after-swap="this.classList.add('text-primary'); setTimeout(() => this.classList.remove('text-primary'), 300)"
                                              th:attr="id='consumption-' + ${spot.id}">Cargando...</span>
                                    </div>
                                </div>
                                <div class="ms-2 d-flex align-items-center">
                                    <button class="btn btn-link consumption-button invisible p-0"
                                            hx-on:click="this.classList.remove('invisible')"
                                            hx-on:htmx:after-request="this.classList.remove('invisible')"
                                            hx-swap="innerHTML"
                                            hx-trigger="revealed once, click"
                                            th:attr="hx-get=@{/unitRead/KWH/{spotId}(spotId=${spot.id})},
                                                    hx-target='#consumption-' + ${spot.id}">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                        </li>
                        <!-- Corriente -->
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-speedometer2 text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Intensidad:</span>
                                        <span hx-on:htmx:after-swap="this.classList.add('text-primary'); setTimeout(() => this.classList.remove('text-primary'), 300)"
                                              th:attr="id='current-' + ${spot.id}">Cargando...</span>
                                    </div>
                                </div>
                                <div class="ms-2 d-flex align-items-center">
                                    <button class="btn btn-link current-button invisible p-0"
                                            hx-on:click="this.classList.remove('invisible')"
                                            hx-on:htmx:after-request="this.classList.remove('invisible')"
                                            hx-swap="innerHTML"
                                            hx-trigger="revealed once, click"
                                            th:attr="hx-get=@{/unitRead/AMPS/{spotId}(spotId=${spot.id})},
                                                    hx-target='#current-' + ${spot.id}">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                        </li>

                        <!-- Potencia -->
                        <li class="list-group-item py-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-plug-fill text-primary me-2"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted small">Potencia:</span>
                                        <span hx-on:htmx:after-swap="this.classList.add('text-primary'); setTimeout(() => this.classList.remove('text-primary'), 300); updateChargingBadge(this);"
                                              th:attr="id='power-' + ${spot.id}">Cargando...</span>
                                    </div>
                                </div>
                                <div class="ms-2 d-flex align-items-center">
                                    <button class="btn btn-link power-button invisible p-0"
                                            hx-on:click="this.classList.remove('invisible')"
                                            hx-on:htmx:after-request="this.classList.remove('invisible')"
                                            hx-swap="innerHTML"
                                            hx-trigger="revealed once, click"
                                            th:attr="hx-get=@{/unitRead/WATTS/{spotId}(spotId=${spot.id})},
                                                    hx-target='#power-' + ${spot.id}">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <!-- Acciones -->
                <div class="card-footer border-top mb-0 py-1">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <!-- Charging badge will appear here when power > 100W -->
                            <span class="badge bg-warning text-dark d-none d-flex align-items-center"
                                  th:attr="id='charging-badge-' + ${spot.id}">
                                <i class="bi bi-plugin fs-6 me-1"></i>
                                <span class="charging-text">Cargando</span>
                            </span>
                            <!-- Inactive badge will appear when power <= 100W or no power -->
                            <span class="badge bg-secondary d-none d-flex align-items-center"
                                  th:attr="id='inactive-badge-' + ${spot.id}">
                                <i class="bi bi-power fs-6 me-1"></i>
                                <span class="inactive-text">Inactivo</span>
                            </span>
                        </div>
                        <div class="d-flex gap-2">
                            <a class="btn btn-sm btn-link p-0"
                               th:href="@{/statistics(spotId=${spot.id}, collapsed='true')}" title="Ver estadísticas">
                                <i class="bi bi-bar-chart-fill fs-5"></i>
                            </a>
                            <a class="btn btn-sm btn-link p-0"
                               th:href="@{/readings/{id}(id=${spot.id}, collapsed='true')}" title="Ver histórico">
                                <i class="bi bi-clock-history fs-5"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Script to handle charging badge visibility -->
<script>
    function updateChargingBadge(powerElement) {
        // Extract spot ID from the power element ID
        const spotId = powerElement.id.replace('power-', '');
        const badgeElement = document.getElementById('charging-badge-' + spotId);
        if (!badgeElement) return;

        // Extract the power value (number before 'W')
        const powerText = powerElement.textContent;
        const powerMatch = powerText.match(/(\d+(\.\d+)?)/); // Match number with optional decimal

        // Get the inactive badge element
        const inactiveBadgeElement = document.getElementById('inactive-badge-' + spotId);

        // Parse power value if available
        let powerValue = 0;
        if (powerMatch && !powerText.includes('Error')) {
            powerValue = parseFloat(powerMatch[0]);
        }

        // Show charging badge if power > 100W, show inactive badge otherwise
        if (powerValue > 100) {
            // Show charging badge
            badgeElement.classList.remove('d-none');
            const textElement = badgeElement.querySelector('.charging-text');
            if (textElement) {
                textElement.textContent = 'Cargando';
            }

            // Hide inactive badge
            if (inactiveBadgeElement) {
                inactiveBadgeElement.classList.add('d-none');
            }
        } else {
            // Hide charging badge
            badgeElement.classList.add('d-none');

            // Show inactive badge
            if (inactiveBadgeElement) {
                inactiveBadgeElement.classList.remove('d-none');
                const inactiveTextElement = inactiveBadgeElement.querySelector('.inactive-text');
                if (inactiveTextElement) {
                    inactiveTextElement.textContent = 'Inactivo';
                }
            }
        }
    }

    // Initialize badges when page loads
    document.addEventListener('DOMContentLoaded', function () {
        // Find all power elements and update their badges
        document.querySelectorAll('[id^="power-"]').forEach(function (powerElement) {
            updateChargingBadge(powerElement);
        });
    });
</script>

<!-- Include toast notifications -->
<th:block th:replace="~{layout/toast-include :: toast-include}"></th:block>
</body>
</html>
