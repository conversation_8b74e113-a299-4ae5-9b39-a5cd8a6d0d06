<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Estado de las plantas</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Estado de las plantas')}"></th:block>
<th:block th:replace="~{layout/navbar :: floating-action('')}"></th:block>
<div class="container-sm mb-5">
    <!-- Summary card with refresh button -->
    <div class="card shadow-sm mb-4">
        <div class="card-header py-2 py-md-2 d-flex justify-content-between align-items-center">
            <h5 class="card-title d-flex align-items-center mb-0">
                <i class="bi bi-speedometer2 me-2"></i>
                Resumen
            </h5>
            <div class="d-flex align-items-center p-0">
                <a class="btn btn-sm btn-link p-0"
                   hx-push-url="true"
                   hx-swap="innerHTML"
                   hx-target="body"
                   th:attr="hx-get=@{/status}">
                    <i class="bi bi-arrow-clockwise fs-5"></i>
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <ul class="list-group list-group-flush">
                <li class="list-group-item p-3 d-flex justify-content-between align-items-center"
                    th:classappend="${floorReading.isFloorOk() ? '' : 'list-group-item-danger'}"
                    th:each="floorReading : ${readingsByFloor}">
                    <div class="d-flex align-items-center">
                        <i th:class="${floorReading.isFloorOk() ? 'bi bi-check-circle-fill text-success' : 'bi bi-exclamation-triangle-fill text-danger'} + ' me-2 fs-5'"></i>
                        <span class="fw-medium">Sótano <span th:text="${floorReading.floorNumber()}"></span></span>
                    </div>
                    <div>
                        <span class="badge bg-warning text-dark rounded-pill me-2"
                              th:if="${floorReading.getChargingSpotsCount() > 0}"
                              th:text="${floorReading.getChargingSpotsCount()} + ' cargando'"></span>
                        <span class="badge bg-success rounded-pill" th:if="${floorReading.isFloorOk()}"
                              th:text="${floorReading.totalPower()} + 'W'"></span>
                        <span class="badge bg-danger rounded-pill" th:unless="${floorReading.isFloorOk()}">ERROR</span>
                    </div>
                </li>
            </ul>
        </div>
        <div class="card-footer text-muted py-2 text-center">
            <small>
                <i class="bi bi-clock me-1"></i>
                Última actualización: <span th:text="${formattedTimestamp}"></span>
            </small>
        </div>
    </div>

    <!-- Floor cards -->
    <div class="card shadow-sm mb-4" th:each="floorReading : ${readingsByFloor}">
        <div class="card-header py-2 py-md-2 d-flex justify-content-between align-items-center"
             th:classappend="${floorReading.isFloorOk() ? '' : 'text-white bg-danger'}">
            <h5 class="card-title mb-0">
                <i th:class="${floorReading.isFloorOk() ? 'bi bi-building-fill' : 'bi bi-building-exclamation'} + ' me-2'"></i>
                Sótano <span th:text="${floorReading.floorNumber()}"></span>
            </h5>
            <div>
                <span class="badge bg-light text-dark" th:if="${floorReading.isFloorOk()}">
                    <i class="bi bi-lightning-charge-fill text-warning me-1 fs-6"></i>
                    <span th:text="${floorReading.totalPower()} + 'W'"></span>
                </span>
                <span class="badge bg-light text-danger" th:unless="${floorReading.isFloorOk()}">
                    <i class="bi bi-exclamation-triangle-fill me-1"></i>
                    ERROR
                </span>
            </div>
        </div>
        <div class="card-body p-0">
            <ul class="list-group list-group-flush">
                <!-- Show message if no spots -->
                <li class="list-group-item p-3 text-center text-muted" th:if="${floorReading.readings().isEmpty()}">
                    <i class="bi bi-info-circle me-2 fs-5"></i>
                    No hay plazas configuradas en esta planta
                </li>
                <!-- List all spots -->
                <li class="list-group-item p-3" th:each="spot : ${floorReading.readings()}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <!-- Icon based on status: charging, inactive, or error -->
                            <div class="d-flex align-items-center mb-1">
                                <i th:class="${spot.isSuccess() ? (spot.getReading() > 100 ? 'bi bi-plugin text-warning' : 'bi bi-p-square-fill text-primary') : 'bi bi-x-circle-fill text-danger'} + ' me-2 fs-5'"></i>
                                <span class="fw-medium" th:text="${'Plaza ' + spot.getSpot().getSpotNumber()}"></span>
                            </div>
                            <!-- Customer info on new line -->
                            <div class="text-muted ms-4 mt-1" th:if="${spot.getSpot().getCustomer() != null}">
                                <i class="bi bi-person-fill me-1"></i>
                                <span th:text="${spot.getSpot().getCustomer().getFirstName() + ' ' + spot.getSpot().getCustomer().getLastName()}"></span>
                            </div>
                        </div>
                        <div>
                            <!-- Show charging badge if > 100W -->
                            <span class="badge bg-warning text-dark rounded-pill" th:if="${spot.isSuccess() && spot.getReading() > 100}">
                                <i class="bi bi-battery-charging me-1"></i>
                                <span th:text="${spot.getRoundedReading() + 'W'}"></span>
                            </span>
                            <!-- Show inactive badge if reading <= 100W -->
                            <span class="badge bg-secondary rounded-pill" th:if="${spot.isSuccess() && spot.getReading() <= 100}">
                                <i class="bi bi-power me-1"></i>
                                Inactivo
                            </span>
                            <!-- Show error badge if not successful -->
                            <span class="badge bg-danger rounded-pill" th:unless="${spot.isSuccess()}">
                                <i class="bi bi-exclamation-triangle-fill me-1"></i>
                                Error
                            </span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="card-footer text-muted py-2">
            <div class="d-flex justify-content-between align-items-center">
                <small>
                    <i class="bi bi-info-circle me-1"></i>
                    <span th:text="${floorReading.readings().size()} + ' plazas en total'"></span>
                </small>
                <small>
                    <i class="bi bi-plugin text-warning me-1"></i>
                    <span th:text="${floorReading.getChargingSpotsCount()} + ' plazas cargando'"></span>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Include toast notifications -->
<th:block th:replace="~{layout/toast-include :: toast-include}"></th:block>
</body>
</html>
