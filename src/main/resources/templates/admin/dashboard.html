<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org" 
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>Panel de administración</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Panel de administración')}"></th:block>
<div class="container-sm mb-5">
    <!-- Welcome card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <h5 class="card-title">
                <i class="bi bi-person-circle me-2"></i>
                Bienvenido, <span sec:authentication="name">Usuario</span>
            </h5>
            <p class="card-text">
                Este es el panel de administración de EVModbus. Desde aquí puedes gestionar los usuarios y roles del sistema.
            </p>
        </div>
    </div>
    
    <!-- Admin modules -->
    <div class="row g-4">
        <!-- User management card -->
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people-fill me-2"></i>
                        Gestión de usuarios
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        Administra los usuarios del sistema, sus roles y permisos.
                    </p>
                    <ul class="list-group list-group-flush mb-3">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total de usuarios
                            <span class="badge bg-primary rounded-pill" th:text="${users.size()}">0</span>
                        </li>
                    </ul>
                </div>
                <div class="card-footer bg-light">
                    <a th:href="@{/admin/users}" class="btn btn-outline-primary w-100">
                        <i class="bi bi-arrow-right-circle me-2"></i>
                        Gestionar usuarios
                    </a>
                </div>
            </div>
        </div>
        
        <!-- System info card -->
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        Información del sistema
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        Información general sobre el sistema EVModbus.
                    </p>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Versión
                            <span class="badge bg-primary rounded-pill">1.0.0</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Entorno
                            <span class="badge bg-primary rounded-pill" th:text="${@environment.getActiveProfiles().length > 0 ? @environment.getActiveProfiles()[0] : 'default'}">default</span>
                        </li>
                    </ul>
                </div>
                <div class="card-footer bg-light">
                    <a th:href="@{/status}" class="btn btn-outline-primary w-100">
                        <i class="bi bi-arrow-left-circle me-2"></i>
                        Volver al sistema
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
