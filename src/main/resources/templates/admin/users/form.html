<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <title th:text="${user.id == null ? 'Nuevo usuario' : 'Editar usuario'}">Gestión de usuario</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar(${user.id == null ? 'Nuevo usuario' : 'Editar usuario'})}"></th:block>
<div class="container-sm col-lg-8 col-xl-6 mb-5">
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-person-fill me-2"></i>
                <span th:text="${user.id == null ? 'Nuevo usuario' : 'Editar usuario'}">Gestión de usuario</span>
            </h5>
        </div>
        <div class="card-body">
            <form id="userForm" th:action="@{/admin/users}" th:object="${user}" method="post" onsubmit="convertUsernameToLowercase()">
                <!-- Hidden ID field for updates -->
                <input type="hidden" th:field="*{id}" />

                <div class="row g-3">
                    <!-- First row: Username and Full name -->
                    <div class="col-md-6">
                        <label for="username" class="form-label">Nombre de usuario</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-person-fill"></i></span>
                            <input type="text" class="form-control" id="username" th:field="*{username}"
                                   required th:readonly="${user.id != null}"
                                   placeholder="Introduce el nombre de usuario">
                        </div>
                        <div class="form-text">El nombre de usuario debe ser único y no puede cambiarse después. No distingue entre mayúsculas y minúsculas.</div>
                    </div>



                    <!-- Second row: Password and Status -->
                    <div class="col-md-6">
                        <label for="password" class="form-label">Contraseña</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                            <input type="password" class="form-control" id="password" th:field="*{password}"
                                   th:required="${user.id == null}"
                                   placeholder="Introduce la contraseña">
                        </div>
                        <div class="form-text" th:if="${user.id != null}">
                            Deja en blanco para mantener la contraseña actual.
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Rol</label>
                        <select class="form-select" name="roleId" required>
                            <option value="">Selecciona un rol</option>
                            <option th:each="role : ${roles}"
                                    th:value="${role.id}"
                                    th:text="${role.name}"
                                    th:selected="${selectedRole != null && selectedRole.id == role.id}">ROLE</option>
                        </select>
                        <div class="form-text">Selecciona el rol que tendrá este usuario.</div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Estado</label>
                        <div class="d-flex align-items-center">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enabled" th:field="*{enabled}">
                                <label class="form-check-label" for="enabled">Usuario activo</label>
                            </div>
                        </div>
                        <div class="form-text">Los usuarios inactivos no pueden iniciar sesión.</div>
                    </div>

                    <!-- Third row: Role selection -->

                </div>

            </form>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between">
                <a th:href="@{/admin/users}" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-2"></i>
                    Cancelar
                </a>
                <button type="submit" form="userForm" class="btn btn-outline-primary">
                    <i class="bi bi-save me-2"></i>
                    Guardar
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Script to convert username to lowercase before form submission -->
<script>
    function convertUsernameToLowercase() {
        const usernameInput = document.getElementById('username');
        if (usernameInput && !usernameInput.readOnly) {
            usernameInput.value = usernameInput.value.toLowerCase();
        }
    }
</script>
</body>
</html>
