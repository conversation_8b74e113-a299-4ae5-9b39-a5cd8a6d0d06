<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>Gestión de usuarios</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<th:block th:replace="~{layout/navbar :: navbar('Gestión de usuarios')}"></th:block>
<div class="container-sm mb-5">
    <!-- Alerts -->
    <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span th:text="${successMessage}">Operación completada con éxito</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span th:text="${errorMessage}">Ha ocurrido un error</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- User list card -->
    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-people-fill me-2"></i>Usuarios del sistema
            </h5>
            <a th:href="@{/admin/users/new}" class="btn btn-link btn-sm p-0">
                <i class="bi bi-person-plus-fill fs-5 me-1"></i>
            </a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">ID</th>
                            <th scope="col">Usuario</th>
                            <th scope="col">Rol</th>
                            <th scope="col">Estado</th>
                            <th scope="col" class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:if="${users.empty}">
                            <td colspan="6" class="text-center py-4">
                                <i class="bi bi-exclamation-circle text-muted me-2"></i>
                                No hay usuarios registrados
                            </td>
                        </tr>
                        <tr th:each="user : ${users}">
                            <td th:text="${user.id}">1</td>
                            <td th:text="${user.username}">admin</td>

                            <td>
                                <span class="badge bg-secondary" th:if="${user.role != null}" th:text="${user.role.name}">ROLE</span>
                                <span class="badge bg-light text-dark" th:unless="${user.role != null}">Sin rol</span>
                            </td>
                            <td>
                                <span th:if="${user.enabled}" class="badge bg-success">Activo</span>
                                <span th:unless="${user.enabled}" class="badge bg-danger">Inactivo</span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <a th:href="@{/admin/users/{id}/edit(id=${user.id})}" class="btn btn-outline-primary">
                                        <i class="bi bi-pencil-fill"></i>
                                    </a>
                                    <!-- Hide delete button for current user -->
                                    <a th:if="${!user.username.equals(#authentication.name)}"
                                       th:href="@{/admin/users/{id}/delete(id=${user.id})}"
                                       class="btn btn-outline-danger"
                                       onclick="return confirm('¿Estás seguro de que deseas eliminar este usuario?')">
                                        <i class="bi bi-trash-fill"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <a th:href="@{/admin}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Volver al panel
                </a>
                <span class="text-muted" th:text="${'Total: ' + users.size() + ' usuarios'}">Total: 0 usuarios</span>
            </div>
        </div>
    </div>
</div>
</body>
</html>
