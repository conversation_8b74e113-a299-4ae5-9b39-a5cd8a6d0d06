<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="es">
<head>
    <title>Histórico</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<!-- Define the title variable -->
<th:block th:with="pageTitle=${spot != null ? 'Plaza '+ spot.getSpotNumber() + ' - Sótano '+ spot.getFloor().getFloorNumber() : 'Histórico'}">
    <!-- Pass the variable to the navbar -->
    <th:block th:replace="~{layout/navbar :: navbar(${pageTitle})}"></th:block>
</th:block>
<th:block th:replace="~{layout/navbar :: floating-action('')}"></th:block>
<div class="container-sm mb-5">
    <!-- Page title is now in the navbar -->

    <!-- Filter Form -->
    <div class="card mb-4">
        <a class="card-header d-flex justify-content-between align-items-center text-decoration-none text-dark"
             style="cursor: pointer;"
             th:classappend="${collapsedState != null && collapsedState == 'true' ? 'collapsed' : ''}"
             th:attr="hx-get=@{/readings(spotId=${selectedSpotId}, successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate}, page=${currentPage != null ? currentPage : 0}, size=${size != null ? size : 10}, collapsed=${collapsedState != null && collapsedState == 'true' ? 'false' : 'true'})}"
             hx-push-url="true"
             hx-target="body"
             hx-swap="innerHTML">
            <h5 class="mb-0"><i class="bi bi-funnel-fill me-2"></i>Filtros</h5>
            <i class="bi" th:classappend="${collapsedState != null && collapsedState == 'true' ? 'bi-chevron-down' : 'bi-chevron-up'}"></i>
        </a>
        <div class="collapse" th:classappend="${collapsedState != null && collapsedState == 'true' ? '' : 'show'}" id="filterCollapse">
            <div class="card-body">
                <form th:action="@{/readings}" method="get" id="filterForm">

                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="spotId" class="form-label text-muted small"><i class="bi bi-car-front-fill text-primary me-1"></i>Plaza</label>
                            <select id="spotId" name="spotId" class="form-select"
                                    th:attr="hx-get=@{/readings}"
                                    hx-target="body"
                                    hx-swap="innerHTML"
                                    hx-push-url="true"
                                    hx-include="#filterForm">
                                <option value="">Seleccione una plaza</option>
                                <option th:each="spot : ${spots}" th:value="${spot.id}"
                                        th:text="${'Plaza ' + spot.spotNumber + ' - Sótano ' + spot.floor.floorNumber}"
                                        th:selected="${selectedSpotId != null && selectedSpotId == spot.id}"></option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="successFilter" class="form-label text-muted small"><i class="bi bi-check-circle-fill text-primary me-1"></i>Estado de lectura</label>
                            <select id="successFilter" name="successFilter" class="form-select"
                                    th:attr="hx-get=@{/readings}"
                                    hx-target="body"
                                    hx-swap="innerHTML"
                                    hx-push-url="true"
                                    hx-include="#filterForm">
                                <option value="all" th:selected="${successFilter == 'all'}">Todas las lecturas</option>
                                <option value="success" th:selected="${successFilter == 'success'}">Solo lecturas exitosas</option>
                                <option value="failed" th:selected="${successFilter == 'failed'}">Solo lecturas fallidas</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="startDate" class="form-label text-muted small"><i class="bi bi-calendar-date text-primary me-1"></i>Fecha y hora inicio</label>
                            <input type="datetime-local" id="startDate" name="startDate" class="form-control" th:value="${startDate}"
                                   th:attr="hx-get=@{/readings}"
                                   hx-target="body"
                                   hx-swap="innerHTML"
                                   hx-push-url="true"
                                   hx-include="#filterForm"
                                   hx-trigger="change">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="endDate" class="form-label text-muted small"><i class="bi bi-calendar-date text-primary me-1"></i>Fecha y hora fin</label>
                            <input type="datetime-local" id="endDate" name="endDate" class="form-control" th:value="${endDate}"
                                   th:attr="hx-get=@{/readings}"
                                   hx-target="body"
                                   hx-swap="innerHTML"
                                   hx-push-url="true"
                                   hx-include="#filterForm"
                                   hx-trigger="change">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="size" class="form-label text-muted small"><i class="bi bi-list-ol text-primary me-1"></i>Elementos por página</label>
                            <select id="size" name="size" class="form-select"
                                    th:attr="hx-get=@{/readings}"
                                    hx-target="body"
                                    hx-swap="innerHTML"
                                    hx-push-url="true"
                                    hx-include="#filterForm">
                                <option value="10" th:selected="${size == 10}">10</option>
                                <option value="20" th:selected="${size == 20}">20</option>
                                <option value="50" th:selected="${size == 50}">50</option>
                                <option value="100" th:selected="${size == 100}">100</option>
                            </select>
                        </div>
                    </div>
                    <div class="d-flex flex-column flex-md-row justify-content-between gap-2">
                        <!-- Filter buttons group -->
                        <div class="btn-group">
                            <a th:if="${selectedSpotId != null}" th:href="@{/readings/{id}(id=${selectedSpotId}, size=${size}, collapsed=${collapsedState != null ? collapsedState : 'false'})}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Limpiar filtros
                            </a>
                            <a th:if="${selectedSpotId != null}" th:href="@{/statistics(spotId=${selectedSpotId}, collapsed='true')}" class="btn btn-outline-primary">
                                <i class="bi bi-bar-chart-fill"></i> Ver estadísticas
                            </a>
                            <a th:unless="${selectedSpotId != null}" th:href="@{/readings(size=${size}, collapsed=${collapsedState != null ? collapsedState : 'false'})}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Limpiar filtros
                            </a>
                        </div>

                        <!-- Download buttons group -->
                        <div class="btn-group">
                            <!-- Download CSV button - only show when a spot is selected -->
                            <a class="btn btn-success" hx-boost="false" th:if="${selectedSpotId != null}" th:href="@{/readings/download/csv(spotId=${selectedSpotId}, successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate})}">
                                <i class="bi bi-file-earmark-excel"></i> Descargar CSV de esta plaza
                            </a>
                            <!-- Download all spots CSV button -->
                            <a class="btn btn-outline-success" hx-boost="false" th:href="@{/readings/download/csv(successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate})}">
                                <i class="bi bi-file-earmark-excel"></i> Descargar CSV de todas las plazas
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Message when no spot is selected -->
    <div class="alert alert-info" th:if="${selectedSpotId == null}">
        Por favor, seleccione una plaza para ver las lecturas.
    </div>



    <!-- No results message - only show when a spot is selected -->
    <div class="alert alert-info" th:if="${selectedSpotId != null && readings.isEmpty()}">
        No hay lecturas que coincidan con los filtros seleccionados.
    </div>

    <!-- Results card - only show when a spot is selected -->
    <div class="card shadow-sm" th:if="${selectedSpotId != null && !readings.isEmpty()}">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Lecturas
            </h5>
        </div>
        <div class="card-body p-0">
            <ul class="list-group list-group-flush">
                <li class="list-group-item" th:each="reading : ${readings}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <!-- Desktop view (inline) -->
                            <div class="d-none d-md-block">
                                <span class="fw-bold" th:text="${#temporals.format(reading.dateTime, 'dd/MM/yyyy - HH:mm')}"></span>&nbsp;
                                <i class="bi bi-arrow-right">&nbsp;&nbsp;</i><span th:text="${reading.success == true} ? ${reading.reading + ' kW/h'} : 'error'"></span>
                            </div>

                            <!-- Mobile view (stacked) -->
                            <div class="d-md-none">
                                <div class="fw-bold" th:text="${#temporals.format(reading.dateTime, 'dd/MM/yyyy - HH:mm')}"></div>
                                <div th:text="${reading.success == true} ? ${reading.reading + ' kW/h'} : 'error'">
                                </div>
                            </div>
                        </div>
                        <div>
                            <a class="btn btn-sm btn-link p-0"
                               hx-confirm="¿Desea eliminar esta lectura?"
                               hx-swap="delete"
                               hx-target="closest li"
                               sec:authorize="hasRole('ADMIN')"
                               th:attr="hx-delete=@{/readings/{id}(id=${reading.id})}">
                                <i class="bi bi-trash3 text-danger fs-5"></i>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <!-- Card footer with pagination - only show when pagination is needed -->
        <div class="card-footer text-center py-2" th:if="${totalPages > 0}">
            <div class="d-flex justify-content-center align-items-center gap-3">
                <!-- First page -->
                <a class="text-decoration-none" th:classappend="${currentPage == 0} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == 0} ? '#' : @{/readings(spotId=${selectedSpotId}, successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate}, page=0, size=${size}, collapsed=${collapsedState})}">
                    <i class="bi bi-chevron-double-left fs-5"></i>
                </a>

                <!-- Previous page -->
                <a class="text-decoration-none" th:classappend="${currentPage == 0} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == 0} ? '#' : @{/readings(spotId=${selectedSpotId}, successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate}, page=${currentPage - 1}, size=${size}, collapsed=${collapsedState})}">
                    <i class="bi bi-chevron-left fs-5"></i>
                </a>

                <!-- Page numbers -->
                <div class="d-flex gap-2 align-items-center">
                    <a th:each="i : ${#numbers.sequence(startPage, endPage)}"
                       class="text-decoration-none rounded-circle d-inline-flex justify-content-center align-items-center"
                       style="width: 30px; height: 30px;"
                       th:classappend="${i == currentPage} ? 'bg-primary text-white fw-bold' : 'text-primary'"
                       th:href="@{/readings(spotId=${selectedSpotId}, successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate}, page=${i}, size=${size}, collapsed=${collapsedState})}"
                       th:text="${i + 1}">1</a>
                </div>

                <!-- Next page -->
                <a class="text-decoration-none" th:classappend="${currentPage == totalPages - 1} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == totalPages - 1} ? '#' : @{/readings(spotId=${selectedSpotId}, successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate}, page=${currentPage + 1}, size=${size}, collapsed=${collapsedState})}">
                    <i class="bi bi-chevron-right fs-5"></i>
                </a>

                <!-- Last page -->
                <a class="text-decoration-none" th:classappend="${currentPage == totalPages - 1} ? 'text-muted' : 'text-primary'"
                   th:href="${currentPage == totalPages - 1} ? '#' : @{/readings(spotId=${selectedSpotId}, successFilter=${successFilter}, startDate=${startDate}, endDate=${endDate}, page=${totalPages - 1}, size=${size}, collapsed=${collapsedState})}">
                    <i class="bi bi-chevron-double-right fs-5"></i>
                </a>
            </div>
        </div>
    </div>
</div>
<style>
    /* Fix for the visual glitch with the extra pixel line */
    .card-header.collapsed {
        border-bottom: 0 !important;
    }

    /* Ensure the card header has a border when expanded */
    .card-header:not(.collapsed) {
        border-bottom: 1px solid rgba(0,0,0,.125) !important;
    }
</style>

<!-- Include toast notifications -->
<th:block th:replace="~{layout/toast-include :: toast-include}"></th:block>
</body>
</html>
