package com.hakcu.evmodbus.enums;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Enum representing different time units for statistics calculations.
 * Each time unit provides methods for generating time periods, formatting period labels,
 * and calculating extended date ranges for data retrieval.
 */
public enum TimeUnit {
    HOUR {
        @Override
        public List<LocalDateTime> generatePeriods(LocalDateTime start, LocalDateTime end) {
            List<LocalDateTime> periods = new ArrayList<>();
            LocalDateTime current = start.withMinute(0).withSecond(0).withNano(0);
            while (!current.isAfter(end)) {
                periods.add(current);
                current = current.plusHours(1);
            }
            return periods;
        }

        @Override
        public String formatPeriod(LocalDateTime periodStart) {
            int hour = periodStart.getHour();
            String nextHour = (hour == 23) ? "00:00" : String.format("%02d:00", hour + 1);
            return String.format("%02d:00 - %s", hour, nextHour);
        }

        @Override
        public LocalDateTime getExtendedStart(LocalDateTime start) {
            return start.minusHours(1);
        }

        @Override
        public LocalDateTime getExtendedEnd(LocalDateTime end) {
            return end.plusHours(1);
        }

        @Override
        public LocalDateTime getEndOfPeriod(LocalDateTime periodStart) {
            return periodStart.plusHours(1).minusNanos(1);
        }
    },
    DAY {
        @Override
        public List<LocalDateTime> generatePeriods(LocalDateTime start, LocalDateTime end) {
            List<LocalDateTime> periods = new ArrayList<>();
            LocalDate currentDate = start.toLocalDate();
            LocalDate endDate = end.toLocalDate();
            while (!currentDate.isAfter(endDate)) {
                periods.add(currentDate.atStartOfDay());
                currentDate = currentDate.plusDays(1);
            }
            return periods;
        }

        @Override
        public String formatPeriod(LocalDateTime periodStart) {
            return periodStart.format(DAY_FORMATTER);
        }

        @Override
        public LocalDateTime getExtendedStart(LocalDateTime start) {
            return start.toLocalDate().atStartOfDay().minusDays(1);
        }

        @Override
        public LocalDateTime getExtendedEnd(LocalDateTime end) {
            return end.toLocalDate().atTime(23, 59, 59).plusDays(1);
        }

        @Override
        public LocalDateTime getEndOfPeriod(LocalDateTime periodStart) {
            return periodStart.toLocalDate().atTime(23, 59, 59);
        }
    },
    MONTH {
        @Override
        public List<LocalDateTime> generatePeriods(LocalDateTime start, LocalDateTime end) {
            List<LocalDateTime> periods = new ArrayList<>();
            YearMonth currentMonth = YearMonth.from(start);
            YearMonth endMonth = YearMonth.from(end);
            while (!currentMonth.isAfter(endMonth)) {
                periods.add(currentMonth.atDay(1).atStartOfDay());
                currentMonth = currentMonth.plusMonths(1);
            }
            return periods;
        }

        @Override
        public String formatPeriod(LocalDateTime periodStart) {
            YearMonth month = YearMonth.from(periodStart);
            return formatMonthName(month);
        }

        @Override
        public LocalDateTime getExtendedStart(LocalDateTime start) {
            return start.withDayOfMonth(1).minusMonths(1).withHour(0).withMinute(0).withSecond(0);
        }

        @Override
        public LocalDateTime getExtendedEnd(LocalDateTime end) {
            LocalDate endDate = end.toLocalDate();
            LocalDate lastDayOfMonth = endDate.with(TemporalAdjusters.lastDayOfMonth());
            return lastDayOfMonth.plusMonths(1).atTime(23, 59, 59);
        }

        @Override
        public LocalDateTime getEndOfPeriod(LocalDateTime periodStart) {
            YearMonth month = YearMonth.from(periodStart);
            return month.atEndOfMonth().atTime(23, 59, 59);
        }

        private String formatMonthName(YearMonth month) {
            String monthName = month.getMonth().getDisplayName(TextStyle.SHORT, SPANISH_LOCALE);
            // Capitalize first letter and ensure it's a 3-letter abbreviation
            return monthName.substring(0, 1).toUpperCase() + monthName.substring(1, Math.min(3, monthName.length()));
        }
    };

    private static final Locale SPANISH_LOCALE = Locale.of("es", "ES");
    private static final DateTimeFormatter DAY_FORMATTER = DateTimeFormatter.ofPattern("dd/MM");

    /**
     * Generates a list of time periods between the start and end dates.
     *
     * @param start The start date/time
     * @param end The end date/time
     * @return A list of LocalDateTime objects representing the start of each period
     */
    public abstract List<LocalDateTime> generatePeriods(LocalDateTime start, LocalDateTime end);

    /**
     * Formats a period start time into a human-readable string.
     *
     * @param periodStart The start of the period
     * @return A formatted string representation of the period
     */
    public abstract String formatPeriod(LocalDateTime periodStart);

    /**
     * Gets an extended start time for data retrieval, typically one period before the requested start.
     *
     * @param start The original start time
     * @return The extended start time
     */
    public abstract LocalDateTime getExtendedStart(LocalDateTime start);

    /**
     * Gets an extended end time for data retrieval, typically one period after the requested end.
     *
     * @param end The original end time
     * @return The extended end time
     */
    public abstract LocalDateTime getExtendedEnd(LocalDateTime end);

    /**
     * Gets the end time of a period given its start time.
     *
     * @param periodStart The start of the period
     * @return The end of the period
     */
    public abstract LocalDateTime getEndOfPeriod(LocalDateTime periodStart);
}
