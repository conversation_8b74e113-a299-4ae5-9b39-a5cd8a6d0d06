package com.hakcu.evmodbus.exceptions;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Global exception handler for the application.
 * Provides consistent error handling across all controllers.
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    private static final String ERROR_VIEW = "error/general";
    private static final String ERROR_MESSAGE_ATTRIBUTE = "errorMessage";
    private static final String ERROR_DETAILS_ATTRIBUTE = "errorDetails";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Determines if the request is an AJAX request.
     * @param request The HTTP request
     * @return true if the request is an AJAX request, false otherwise
     */
    private boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWithHeader = request.getHeader("X-Requested-With");
        String htmxRequest = request.getHeader("HX-Request");
        String acceptHeader = request.getHeader("Accept");

        return "XMLHttpRequest".equals(requestedWithHeader) ||
               "true".equals(htmxRequest) ||
               (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_JSON_VALUE));
    }

    /**
     * Writes a JSON error response.
     * @param response The HTTP response
     * @param status The HTTP status code
     * @param title The error title
     * @param message The error message
     * @param type The error type (for styling)
     */
    private void writeJsonResponse(HttpServletResponse response, HttpStatus status, String title, String message, String type) throws IOException {
        Map<String, Object> errorAttributes = new HashMap<>();
        errorAttributes.put("status", status.value());
        errorAttributes.put("error", status.getReasonPhrase());
        errorAttributes.put("title", title);
        errorAttributes.put("message", message);
        errorAttributes.put("type", type);

        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(status.value());
        response.getWriter().write(objectMapper.writeValueAsString(errorAttributes));
    }

    /**
     * Handles EntityNotFoundException.
     * Returns a 404 Not Found response with an error view or JSON.
     */
    @ExceptionHandler(EntityNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ModelAndView handleEntityNotFoundException(HttpServletRequest request, HttpServletResponse response, EntityNotFoundException ex) throws IOException {
        logger.error("Entity not found at {}: {}", request.getRequestURI(), ex.getMessage());

        if (isAjaxRequest(request)) {
            writeJsonResponse(response, HttpStatus.NOT_FOUND, "Recurso no encontrado", ex.getMessage(), "warning");
            return null;
        }

        ModelAndView modelAndView = new ModelAndView(ERROR_VIEW);
        modelAndView.addObject(ERROR_MESSAGE_ATTRIBUTE, "Recurso no encontrado");
        modelAndView.addObject(ERROR_DETAILS_ATTRIBUTE, ex.getMessage());
        modelAndView.addObject("status", HttpStatus.NOT_FOUND.value());
        modelAndView.addObject("error", HttpStatus.NOT_FOUND.getReasonPhrase());
        modelAndView.addObject("path", request.getRequestURI());
        modelAndView.addObject("headerClass", "bg-warning");
        modelAndView.addObject("textClass", "text-dark");
        modelAndView.addObject("iconClass", "bi-exclamation-triangle-fill");
        return modelAndView;
    }

    /**
     * Handles IllegalArgumentException.
     * Returns a 400 Bad Request response with an error view or JSON.
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ModelAndView handleIllegalArgumentException(HttpServletRequest request, HttpServletResponse response, IllegalArgumentException ex) throws IOException {
        logger.error("Invalid argument at {}: {}", request.getRequestURI(), ex.getMessage());

        if (isAjaxRequest(request)) {
            writeJsonResponse(response, HttpStatus.BAD_REQUEST, "Solicitud inválida", ex.getMessage(), "error");
            return null;
        }

        ModelAndView modelAndView = new ModelAndView(ERROR_VIEW);
        modelAndView.addObject(ERROR_MESSAGE_ATTRIBUTE, "Solicitud inválida");
        modelAndView.addObject(ERROR_DETAILS_ATTRIBUTE, ex.getMessage());
        modelAndView.addObject("status", HttpStatus.BAD_REQUEST.value());
        modelAndView.addObject("error", HttpStatus.BAD_REQUEST.getReasonPhrase());
        modelAndView.addObject("path", request.getRequestURI());
        modelAndView.addObject("headerClass", "bg-danger");
        modelAndView.addObject("textClass", "text-white");
        modelAndView.addObject("iconClass", "bi-exclamation-circle-fill");
        return modelAndView;
    }

    /**
     * Handles MissingServletRequestParameterException.
     * Returns a 400 Bad Request response with an error view or JSON.
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ModelAndView handleMissingServletRequestParameterException(HttpServletRequest request, HttpServletResponse response, MissingServletRequestParameterException ex) throws IOException {
        logger.error("Missing parameter at {}: {}", request.getRequestURI(), ex.getMessage());

        String paramName = ex.getParameterName();
        String paramType = ex.getParameterType();
        String errorMessage = "Falta el parámetro obligatorio: " + paramName;
        String detailMessage = "El parámetro '" + paramName + "' de tipo '" + paramType + "' es obligatorio";

        if ("period".equals(paramName) && "java.time.YearMonth".equals(paramType)) {
            errorMessage = "Debe seleccionar un período";
            detailMessage = "Por favor, seleccione un período válido (mes y año) para generar la factura";
        }

        if (isAjaxRequest(request)) {
            writeJsonResponse(response, HttpStatus.BAD_REQUEST, errorMessage, detailMessage, "warning");
            return null;
        }

        ModelAndView modelAndView = new ModelAndView(ERROR_VIEW);
        modelAndView.addObject(ERROR_MESSAGE_ATTRIBUTE, errorMessage);
        modelAndView.addObject(ERROR_DETAILS_ATTRIBUTE, detailMessage);
        modelAndView.addObject("status", HttpStatus.BAD_REQUEST.value());
        modelAndView.addObject("error", HttpStatus.BAD_REQUEST.getReasonPhrase());
        modelAndView.addObject("path", request.getRequestURI());
        modelAndView.addObject("headerClass", "bg-warning");
        modelAndView.addObject("textClass", "text-dark");
        modelAndView.addObject("iconClass", "bi-exclamation-triangle-fill");
        return modelAndView;
    }

    /**
     * Handles ModbusDeviceException.
     * Returns a 503 Service Unavailable response with an error view or JSON.
     */
    @ExceptionHandler(ModbusDeviceException.class)
    @ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
    public ModelAndView handleModbusDeviceException(HttpServletRequest request, HttpServletResponse response, ModbusDeviceException ex) throws IOException {
        logger.error("Modbus device error at {}: {}", request.getRequestURI(), ex.getMessage());

        if (isAjaxRequest(request)) {
            writeJsonResponse(response, HttpStatus.SERVICE_UNAVAILABLE, "Error de comunicación con dispositivo", ex.getMessage(), "info");
            return null;
        }

        ModelAndView modelAndView = new ModelAndView(ERROR_VIEW);
        modelAndView.addObject(ERROR_MESSAGE_ATTRIBUTE, "Error de comunicación con dispositivo");
        modelAndView.addObject(ERROR_DETAILS_ATTRIBUTE, ex.getMessage());
        modelAndView.addObject("status", HttpStatus.SERVICE_UNAVAILABLE.value());
        modelAndView.addObject("error", HttpStatus.SERVICE_UNAVAILABLE.getReasonPhrase());
        modelAndView.addObject("path", request.getRequestURI());
        modelAndView.addObject("headerClass", "bg-secondary");
        modelAndView.addObject("textClass", "text-white");
        modelAndView.addObject("iconClass", "bi-plug-fill");
        return modelAndView;
    }

    /**
     * Handles all other exceptions.
     * Returns a 500 Internal Server Error response with an error view or JSON.
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ModelAndView handleGenericException(HttpServletRequest request, HttpServletResponse response, Exception ex) throws IOException {
        logger.error("Unexpected error at {}: {}", request.getRequestURI(), ex.getMessage(), ex);

        if (isAjaxRequest(request)) {
            writeJsonResponse(response, HttpStatus.INTERNAL_SERVER_ERROR, "Error interno del servidor",
                    "Se ha producido un error inesperado. Por favor, contacte con el administrador.", "error");
            return null;
        }

        ModelAndView modelAndView = new ModelAndView(ERROR_VIEW);
        modelAndView.addObject(ERROR_MESSAGE_ATTRIBUTE, "Error interno del servidor");
        modelAndView.addObject(ERROR_DETAILS_ATTRIBUTE, "Se ha producido un error inesperado. Por favor, contacte con el administrador.");
        modelAndView.addObject("status", HttpStatus.INTERNAL_SERVER_ERROR.value());
        modelAndView.addObject("error", HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase());
        modelAndView.addObject("path", request.getRequestURI());
        modelAndView.addObject("headerClass", "bg-danger");
        modelAndView.addObject("textClass", "text-white");
        modelAndView.addObject("iconClass", "bi-exclamation-triangle-fill");
        return modelAndView;
    }
}
