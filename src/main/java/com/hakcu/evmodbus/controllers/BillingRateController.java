package com.hakcu.evmodbus.controllers;

import com.hakcu.evmodbus.entities.BillingRate;
import com.hakcu.evmodbus.services.BillingRateService;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.annotation.Secured;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.math.BigDecimal;
import java.util.List;

/**
 * Controller for billing rate operations.
 */
@Controller
@RequestMapping("/billing/rates")
@Secured({"ROLE_ADMIN", "ROLE_MANAGER"})
public class BillingRateController {

    private final BillingRateService billingRateService;

    public BillingRateController(BillingRateService billingRateService) {
        this.billingRateService = billingRateService;
    }

    /**
     * Lists all billing rates.
     */
    @GetMapping
    public String list(Model model) {
        List<BillingRate> rates = billingRateService.findAll();
        model.addAttribute("rates", rates);
        return "billing/rates/list";
    }

    /**
     * Shows the form to create a new billing rate.
     */
    @GetMapping("/new")
    public String create(Model model) {
        BillingRate billingRate = new BillingRate();
        billingRate.setFixedMonthlyFee(BigDecimal.valueOf(0));
        model.addAttribute("billingRate", billingRate);
        return "billing/rates/form";
    }

    /**
     * Returns the modal form fragment for creating a new billing rate.
     * This endpoint is used by HTMX to load the form into a modal.
     */
    @GetMapping("/modal/new")
    public String createModal(Model model) {
        BillingRate billingRate = new BillingRate();
        billingRate.setFixedMonthlyFee(BigDecimal.valueOf(0));
        model.addAttribute("billingRate", billingRate);
        return "billing/rates/fragments/form-modal :: form-modal(billingRate=${billingRate})";
    }

    /**
     * Shows the form to edit an existing billing rate.
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, Model model) {
        BillingRate billingRate = billingRateService.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Billing rate not found"));
        model.addAttribute("billingRate", billingRate);
        return "billing/rates/form";
    }

    /**
     * Returns the modal form fragment for editing an existing billing rate.
     * This endpoint is used by HTMX to load the form into a modal.
     */
    @GetMapping("/modal/edit/{id}")
    public String editModal(@PathVariable Long id, Model model) {
        BillingRate billingRate = billingRateService.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Billing rate not found"));
        model.addAttribute("billingRate", billingRate);
        return "billing/rates/fragments/form-modal :: form-modal(billingRate=${billingRate})";
    }

    /**
     * Saves a billing rate (create or update).
     */
    @PostMapping
    public String save(@ModelAttribute BillingRate billingRate, RedirectAttributes redirectAttributes, HttpServletResponse response) {
        try {
            billingRateService.save(billingRate);
            redirectAttributes.addFlashAttribute("successMessage",
                    "Tarifa guardada correctamente");

            // Add HX-Redirect header for HTMX requests
            addHtmxRedirectHeaderIfNeeded(response, "/billing/rates");

            return "redirect:/billing/rates";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage",
                    "Error al guardar la tarifa: " + e.getMessage());

            // Add HX-Redirect header for HTMX requests
            addHtmxRedirectHeaderIfNeeded(response, "/billing/rates");

            return "redirect:/billing/rates";
        }
    }

    /**
     * Deletes a billing rate.
     */
    @DeleteMapping("/{id}")
    @ResponseBody
    public void delete(@PathVariable Long id) {
        billingRateService.delete(id);
    }

    /**
     * Helper method to add HX-Redirect header for HTMX requests.
     * This ensures proper redirection after form submission with HTMX.
     */
    private void addHtmxRedirectHeaderIfNeeded(HttpServletResponse response, String redirectUrl) {
        // Add the HX-Redirect header for HTMX to handle the redirect
        response.setHeader("HX-Redirect", redirectUrl);
    }
}
