package com.hakcu.evmodbus.controllers;

import com.hakcu.evmodbus.entities.Billing;
import com.hakcu.evmodbus.services.BillingPdfService;
import com.hakcu.evmodbus.services.BillingService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.ByteArrayOutputStream;

/**
 * Controller for handling billing exports (PDF only).
 */
@Controller
@RequestMapping("/billing/export")
@Secured({"ROLE_ADMIN", "ROLE_MANAGER"})
public class BillingExportController {

    private final BillingService billingService;
    private final BillingPdfService billingPdfService;

    public BillingExportController(BillingService billingService,
                                  BillingPdfService billingPdfService) {
        this.billingService = billingService;
        this.billingPdfService = billingPdfService;
    }

    /**
     * Exports a single billing as PDF.
     */
    @GetMapping("/pdf/{id}")
    public ResponseEntity<byte[]> exportPdf(@PathVariable Long id) {
        return exportBillingPdf(id);
    }

    /**
     * Exports a single billing as PDF by bill number.
     */
    @GetMapping("/pdf/number/{billNumber}")
    public ResponseEntity<byte[]> exportPdfByBillNumber(@PathVariable Long billNumber) {
        try {
            Billing billing = billingService.findByBillNumber(billNumber)
                    .orElseThrow(() -> new EntityNotFoundException("Billing not found"));
            return exportBillingPdf(billing.getId());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Helper method to export a billing as PDF.
     */
    private ResponseEntity<byte[]> exportBillingPdf(Long id) {
        try {
            Billing billing = billingService.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Billing not found"));

            ByteArrayOutputStream pdfOutput = billingPdfService.generateBillingPdf(billing);

            String filename = generatePdfFilename(billing);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(pdfOutput.toByteArray());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generates a filename for a billing PDF file.
     *
     * @param billing The billing
     * @return The filename
     */
    private String generatePdfFilename(Billing billing) {
        String customerName = billing.getCustomer().getLastName().replaceAll("\\s+", "_") +
                "_" + billing.getCustomer().getFirstName().replaceAll("\\s+", "_");
        String period = billing.getBillingPeriod().toString();
        String billNumber = billing.getBillNumber().toString();
        return "factura_" + billNumber + "_" + customerName + "_" + period + ".pdf";
    }
}
