package com.hakcu.evmodbus.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Custom error controller to handle Spring Boot's default error handling.
 * This controller handles errors that are not caught by the GlobalExceptionHandler.
 */
@Controller
public class CustomErrorController implements ErrorController {

    private static final String ERROR_VIEW = "error/general";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Determines if the request is an AJAX request.
     * @param request The HTTP request
     * @return true if the request is an AJAX request, false otherwise
     */
    private boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWithHeader = request.getHeader("X-Requested-With");
        String htmxRequest = request.getHeader("HX-Request");
        String acceptHeader = request.getHeader("Accept");

        return "XMLHttpRequest".equals(requestedWithHeader) ||
               "true".equals(htmxRequest) ||
               (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_JSON_VALUE));
    }

    /**
     * Writes a JSON error response.
     * @param response The HTTP response
     * @param status The HTTP status code
     * @param title The error title
     * @param message The error message
     * @param type The error type (for styling)
     */
    private void writeJsonResponse(HttpServletResponse response, HttpStatus status, String title, String message, String type) throws IOException {
        Map<String, Object> errorAttributes = new HashMap<>();
        errorAttributes.put("status", status.value());
        errorAttributes.put("error", status.getReasonPhrase());
        errorAttributes.put("title", title);
        errorAttributes.put("message", message);
        errorAttributes.put("type", type);

        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(status.value());
        response.getWriter().write(objectMapper.writeValueAsString(errorAttributes));
    }

    /**
     * Handles all errors and redirects to the appropriate error page or returns JSON for AJAX requests.
     *
     * @param request The HTTP request
     * @param response The HTTP response
     * @param model The model to add attributes to
     * @return The name of the error view to render, or null for AJAX requests
     */
    @RequestMapping("/error")
    public String handleError(HttpServletRequest request, HttpServletResponse response, Model model) throws IOException {
        // Get the status code
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);

        // Default to internal server error if status is not available
        int statusCode = status != null ? Integer.parseInt(status.toString()) : 500;

        // Get error message
        Object message = request.getAttribute(RequestDispatcher.ERROR_MESSAGE);
        String errorDetails = message != null ? message.toString() : "Error desconocido";

        // Set appropriate styling based on status code
        String headerClass = "bg-danger";
        String textClass = "text-white";
        String iconClass = "bi-exclamation-triangle-fill";
        String errorMessage = HttpStatus.valueOf(statusCode).getReasonPhrase();
        String errorType = "error";

        if (statusCode == HttpStatus.NOT_FOUND.value()) {
            headerClass = "bg-warning";
            textClass = "text-dark";
            iconClass = "bi-exclamation-triangle-fill";
            errorMessage = "Página no encontrada";
            errorType = "warning";
        } else if (statusCode == HttpStatus.FORBIDDEN.value() || statusCode == HttpStatus.UNAUTHORIZED.value()) {
            headerClass = "bg-danger";
            textClass = "text-white";
            iconClass = "bi-shield-exclamation";
            errorMessage = "Acceso denegado";
            errorType = "error";
        } else if (statusCode >= 400 && statusCode < 500) {
            headerClass = "bg-danger";
            textClass = "text-white";
            iconClass = "bi-exclamation-circle-fill";
            errorMessage = "Solicitud inválida";
            errorType = "error";
        } else if (statusCode >= 500) {
            headerClass = "bg-danger";
            textClass = "text-white";
            iconClass = "bi-exclamation-triangle-fill";
            errorMessage = "Error interno del servidor";
            errorType = "error";
        }

        // For AJAX requests, return JSON
        if (isAjaxRequest(request)) {
            writeJsonResponse(response, HttpStatus.valueOf(statusCode), errorMessage, errorDetails, errorType);
            return null;
        }

        // Add error details to the model for HTML view
        model.addAttribute("errorMessage", errorMessage);
        model.addAttribute("errorDetails", errorDetails);
        model.addAttribute("status", statusCode);
        model.addAttribute("error", HttpStatus.valueOf(statusCode).getReasonPhrase());
        model.addAttribute("path", request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI));
        model.addAttribute("headerClass", headerClass);
        model.addAttribute("textClass", textClass);
        model.addAttribute("iconClass", iconClass);

        // Always use the generic error page
        return ERROR_VIEW;
    }
}
