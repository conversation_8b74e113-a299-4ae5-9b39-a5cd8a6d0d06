package com.hakcu.evmodbus.controllers;

import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.services.PdfReportService;
import com.hakcu.evmodbus.services.StatisticsService;
import com.hakcu.evmodbus.utils.DateTimeUtils;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.ByteArrayOutputStream;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Controller for the statistics page.
 * Handles requests for viewing statistics about spots and readings.
 */
@Controller
@RequestMapping("/statistics")
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);
    private final StatisticsService statisticsService;
    private final PdfReportService pdfReportService;

    public StatisticsController(StatisticsService statisticsService, PdfReportService pdfReportService) {
        this.statisticsService = statisticsService;
        this.pdfReportService = pdfReportService;
    }

    /**
     * Displays the statistics page with three graphs: hourly, daily, and monthly.
     *
     * @param spotId        The ID of the spot to show statistics for.
     * @param selectedDay   The day to show hourly statistics for.
     * @param selectedMonth The month to show daily statistics for.
     * @param selectedYear  The year to show monthly statistics for.
     * @param model         The model to add attributes to.
     * @return The name of the view to render.
     */
    @GetMapping
    public String showStatistics(
            @RequestParam(required = false) Long spotId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear,
            @RequestParam(required = false, defaultValue = "hourly") String activeTab,
            Model model,
            HttpServletRequest request) {

        // Set default values for the filters
        LocalDate today = LocalDate.now();
        if (selectedDay == null) {
            selectedDay = today;
        }
        if (selectedMonth == null) {
            selectedMonth = today.getYear() + "-" + String.format("%02d", today.getMonthValue());
        }
        if (selectedYear == null) {
            selectedYear = today.getYear();
        }

        // Add all spots to the model for the dropdown
        model.addAttribute("spots", statisticsService.getAllSpots());
        model.addAttribute("selectedSpotId", spotId);
        model.addAttribute("selectedDay", selectedDay);
        model.addAttribute("selectedMonth", selectedMonth);
        model.addAttribute("selectedYear", selectedYear);
        model.addAttribute("activeTab", activeTab);

        // If a spot is selected, get statistics
        if (spotId != null) {
            Spot spot = statisticsService.getSpotById(spotId)
                    .orElseThrow(() -> new EntityNotFoundException("Spot not found"));
            model.addAttribute("spot", spot);

            // Determine if the request is from a mobile device
            boolean isMobile = isMobileDevice(request);
            int chartWidth = isMobile ? 400 : 800;
            int chartHeight = isMobile ? 380 : 480;
            String yAxisLabel = "Consumo (kWh)";

            // Check if there are any readings for this spot
            boolean hasAnyReadings = checkIfSpotHasAnyReadings(spotId);
            model.addAttribute("hasAnyReadings", hasAnyReadings);

            if (!hasAnyReadings) {
                model.addAttribute("noDataMessage", "No hay datos de consumo disponibles para esta plaza.");
            }

            // Generate hourly chart
            boolean isCurrentDay = selectedDay.equals(today);
            generateHourlyChart(model, spotId, selectedDay, isCurrentDay, chartWidth, chartHeight, yAxisLabel);

            // Generate daily chart
            YearMonth yearMonth = YearMonth.parse(selectedMonth);
            boolean isCurrentMonth = yearMonth.equals(YearMonth.now());
            generateDailyChart(model, spotId, yearMonth, isCurrentMonth, chartWidth, chartHeight, yAxisLabel);

            // Generate monthly chart
            boolean isCurrentYear = selectedYear.equals(LocalDate.now().getYear());
            generateMonthlyChart(model, spotId, selectedYear, isCurrentYear, chartWidth, chartHeight, yAxisLabel);

            // For backward compatibility, add the combined data
            Map<String, Float> combinedData = new LinkedHashMap<>();
            combinedData.putAll((Map<String, Float>) model.getAttribute("hourlyData"));
            combinedData.putAll((Map<String, Float>) model.getAttribute("dailyData"));
            combinedData.putAll((Map<String, Float>) model.getAttribute("monthlyData"));
            model.addAttribute("timeSeriesData", combinedData);

            // Calculate consumption for today, this month, and this year
            Float todayConsumption = calculateTodayConsumption(spotId);
            Float thisMonthConsumption = calculateThisMonthConsumption(spotId);
            Float thisYearConsumption = calculateThisYearConsumption(spotId);

            // Add statistics to the model
            model.addAttribute("todayConsumption", todayConsumption);
            model.addAttribute("thisMonthConsumption", thisMonthConsumption);
            model.addAttribute("thisYearConsumption", thisYearConsumption);

            // Calculate date ranges for readings links
            addDateRangeAttributes(model, selectedDay, isCurrentDay, selectedMonth, isCurrentMonth, selectedYear, isCurrentYear);
        }

        return "statistics/list";
    }

    /**
     * Handles tab switching via HTMX.
     * Returns the filter container HTML with the appropriate filter visible.
     *
     * @param tabId         The ID of the tab to switch to.
     * @param spotId        The ID of the selected spot.
     * @param selectedDay   The selected day.
     * @param selectedMonth The selected month.
     * @param selectedYear  The selected year.
     * @param model         The model to add attributes to.
     * @return The filter container HTML fragment.
     */
    @GetMapping("/tab-switch")
    public String switchTab(
            @RequestParam String tabId,
            @RequestParam(required = false) Long spotId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear,
            Model model) {

        // Set default values for the filters if they're null
        LocalDate today = LocalDate.now();
        if (selectedDay == null) {
            selectedDay = today;
        }
        if (selectedMonth == null) {
            selectedMonth = today.getYear() + "-" + String.format("%02d", today.getMonthValue());
        }
        if (selectedYear == null) {
            selectedYear = today.getYear();
        }

        // Add attributes to the model
        model.addAttribute("activeTab", tabId);
        model.addAttribute("selectedSpotId", spotId);
        model.addAttribute("selectedDay", selectedDay);
        model.addAttribute("selectedMonth", selectedMonth);
        model.addAttribute("selectedYear", selectedYear);

        // Calculate current period flags for the selected tab
        switch (tabId) {
            case "hourly" -> {
                boolean isCurrentDay = selectedDay.equals(today);
                model.addAttribute("isCurrentDay", isCurrentDay);
            }
            case "daily" -> {
                YearMonth yearMonth = YearMonth.parse(selectedMonth);
                boolean isCurrentMonth = yearMonth.equals(YearMonth.now());
                model.addAttribute("isCurrentMonth", isCurrentMonth);
            }
            case "monthly" -> {
                boolean isCurrentYear = selectedYear.equals(today.getYear());
                model.addAttribute("isCurrentYear", isCurrentYear);
            }
        }

        return "statistics/fragments/filter-container :: filter-container";
    }

    /**
     * Handles navigation between time periods (previous/next day, month, or year).
     * Returns only the chart content and filter container for HTMX partial updates.
     *
     * @param spotId        The ID of the selected spot.
     * @param activeTab     The active tab (hourly, daily, monthly).
     * @param direction     The navigation direction (prev, next).
     * @param selectedDay   The currently selected day.
     * @param selectedMonth The currently selected month.
     * @param selectedYear  The currently selected year.
     * @param model         The model to add attributes to.
     * @return The chart content fragment with updated data.
     */
    @GetMapping("/navigate")
    public String navigateTimePeriod(
            @RequestParam(required = false) Long spotId,
            @RequestParam(required = false, defaultValue = "hourly") String activeTab,
            @RequestParam String direction,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear,
            Model model,
            HttpServletRequest request) {

        // Set default values for the filters if they're null
        LocalDate today = LocalDate.now();
        if (selectedDay == null) {
            selectedDay = today;
        }
        if (selectedMonth == null) {
            selectedMonth = today.getYear() + "-" + String.format("%02d", today.getMonthValue());
        }
        if (selectedYear == null) {
            selectedYear = today.getYear();
        }

        // Calculate new date values based on the active tab and direction
        switch (activeTab) {
            case "hourly" -> {
                // Navigate by day for hourly tab
                if ("prev".equals(direction)) {
                    selectedDay = selectedDay.minusDays(1);
                } else if ("next".equals(direction)) {
                    LocalDate newDay = selectedDay.plusDays(1);
                    // Don't allow navigation to future days
                    if (!newDay.isAfter(today)) {
                        selectedDay = newDay;
                    }
                }
            }
            case "daily" -> {
                // Navigate by month for daily tab
                YearMonth currentYearMonth = YearMonth.parse(selectedMonth);
                if ("prev".equals(direction)) {
                    YearMonth prevMonth = currentYearMonth.minusMonths(1);
                    selectedMonth = prevMonth.toString();
                } else if ("next".equals(direction)) {
                    YearMonth nextMonth = currentYearMonth.plusMonths(1);
                    // Don't allow navigation to future months
                    if (!nextMonth.isAfter(YearMonth.now())) {
                        selectedMonth = nextMonth.toString();
                    }
                }
            }
            case "monthly" -> {
                // Navigate by year for monthly tab
                if ("prev".equals(direction)) {
                    selectedYear = selectedYear - 1;
                } else if ("next".equals(direction)) {
                    int nextYear = selectedYear + 1;
                    // Don't allow navigation to future years
                    if (nextYear <= today.getYear()) {
                        selectedYear = nextYear;
                    }
                }
            }
        }

        // Add basic attributes to the model
        model.addAttribute("activeTab", activeTab);
        model.addAttribute("selectedSpotId", spotId);
        model.addAttribute("selectedDay", selectedDay);
        model.addAttribute("selectedMonth", selectedMonth);
        model.addAttribute("selectedYear", selectedYear);

        // Generate only the specific chart for the active tab
        if (spotId != null) {
            // Determine if the request is from a mobile device
            boolean isMobile = isMobileDevice(request);
            int chartWidth = isMobile ? 400 : 800;
            int chartHeight = isMobile ? 380 : 480;
            String yAxisLabel = "Consumo (kWh)";

            // Generate chart and calculate filtered consumption total based on active tab
            switch (activeTab) {
                case "hourly" -> {
                    boolean isCurrentDay = selectedDay.equals(LocalDate.now());
                    generateHourlyChart(model, spotId, selectedDay, isCurrentDay, chartWidth, chartHeight, yAxisLabel);

                    // Calculate filtered period consumption total
                    LocalDateTime dayStart = selectedDay.atStartOfDay();
                    LocalDateTime dayEnd = isCurrentDay ? LocalDateTime.now() : selectedDay.atTime(LocalTime.MAX);
                    Float filteredConsumption = statisticsService.getTotalConsumption(spotId, dayStart, dayEnd);
                    model.addAttribute("filteredConsumption", filteredConsumption);
                }
                case "daily" -> {
                    YearMonth yearMonth = YearMonth.parse(selectedMonth);
                    boolean isCurrentMonth = yearMonth.equals(YearMonth.now());
                    generateDailyChart(model, spotId, yearMonth, isCurrentMonth, chartWidth, chartHeight, yAxisLabel);

                    // Calculate filtered period consumption total
                    LocalDateTime monthStart = yearMonth.atDay(1).atStartOfDay();
                    LocalDateTime monthEnd = isCurrentMonth ? LocalDateTime.now() : yearMonth.atEndOfMonth().atTime(LocalTime.MAX);
                    Float filteredConsumption = statisticsService.getTotalConsumption(spotId, monthStart, monthEnd);
                    model.addAttribute("filteredConsumption", filteredConsumption);
                }
                case "monthly" -> {
                    boolean isCurrentYear = selectedYear.equals(LocalDate.now().getYear());
                    generateMonthlyChart(model, spotId, selectedYear, isCurrentYear, chartWidth, chartHeight, yAxisLabel);

                    // Calculate filtered period consumption total
                    LocalDateTime yearStart = LocalDate.of(selectedYear, 1, 1).atStartOfDay();
                    LocalDateTime yearEnd = isCurrentYear ? LocalDateTime.now() : LocalDate.of(selectedYear, 12, 31).atTime(LocalTime.MAX);
                    Float filteredConsumption = statisticsService.getTotalConsumption(spotId, yearStart, yearEnd);
                    model.addAttribute("filteredConsumption", filteredConsumption);
                }
            }
        }

        // Return the chart content and filter container fragments
        return "statistics/fragments/chart-content :: chart-content-with-filter";
    }

    /**
     * Downloads a PDF report for the selected spot and time period.
     *
     * @param spotId        The ID of the spot.
     * @param reportType    The type of report (day, month or year).
     * @param selectedDay   The day for daily reports.
     * @param selectedMonth The month for monthly reports.
     * @param selectedYear  The year for yearly reports.
     * @return ResponseEntity with the PDF file.
     */
    @GetMapping("/pdf")
    public ResponseEntity<byte[]> downloadPdf(
            @RequestParam Long spotId,
            @RequestParam String reportType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear) {

        try {
            ByteArrayOutputStream pdfOutput;
            String filename;

            // Get spot information for the filename
            Spot spot = statisticsService.getSpotById(spotId)
                    .orElseThrow(() -> new EntityNotFoundException("Spot not found"));

            // Get current date/time for comparison
            LocalDate today = LocalDate.now();
            YearMonth currentMonth = YearMonth.now();
            int currentYear = today.getYear();

            switch (reportType) {
                case "day" -> {
                    // Generate daily report
                    if (selectedDay == null) {
                        selectedDay = today;
                    }

                    // Check if selected day is today to limit data to current time
                    boolean isCurrentDay = selectedDay.equals(today);

                    pdfOutput = pdfReportService.generateDailyReport(spotId, selectedDay, isCurrentDay);

                    String formattedDay = selectedDay.format(DateTimeFormatter.ofPattern("dd_MM_yyyy"));
                    String currentTimeSuffix = isCurrentDay ? "_hasta_" + LocalTime.now().format(DateTimeFormatter.ofPattern("HH_mm")) : "";

                    filename = String.format("informe_diario_plaza_%d_sotano_%d_%s%s.pdf",
                            spot.getSpotNumber(), spot.getFloor().getFloorNumber(),
                            formattedDay, currentTimeSuffix);

                }
                case "month" -> {
                    // Generate monthly report
                    YearMonth yearMonth;
                    if (selectedMonth != null) {
                        yearMonth = YearMonth.parse(selectedMonth);
                    } else {
                        yearMonth = currentMonth;
                    }

                    // Check if selected month is current month to limit data to current time
                    boolean isCurrentMonth = yearMonth.equals(currentMonth);

                    pdfOutput = pdfReportService.generateMonthlyReport(spotId, yearMonth, isCurrentMonth);

                    String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.of("es", "ES"));
                    String currentTimeSuffix = isCurrentMonth ? "_hasta_" + today.format(DateTimeFormatter.ofPattern("dd_MM")) : "";

                    filename = String.format("informe_mensual_plaza_%d_sotano_%d_%s_%d%s.pdf",
                            spot.getSpotNumber(), spot.getFloor().getFloorNumber(),
                            monthName, yearMonth.getYear(), currentTimeSuffix);

                }
                case "year" -> {
                    // Generate yearly report
                    int year = (selectedYear != null) ? selectedYear : currentYear;

                    // Check if selected year is current year to limit data to current time
                    boolean isCurrentYear = year == currentYear;

                    pdfOutput = pdfReportService.generateYearlyReport(spotId, year, isCurrentYear);

                    String currentTimeSuffix = isCurrentYear ? "_hasta_" + currentMonth.getMonth().getDisplayName(TextStyle.SHORT, Locale.of("es", "ES")) : "";

                    filename = String.format("informe_anual_plaza_%d_sotano_%d_%d%s.pdf",
                            spot.getSpotNumber(), spot.getFloor().getFloorNumber(), year, currentTimeSuffix);

                }
                case null, default -> {
                    // Default to current day if no valid report type
                    boolean isCurrentDay = true;
                    pdfOutput = pdfReportService.generateDailyReport(spotId, today, isCurrentDay);

                    String formattedDay = today.format(DateTimeFormatter.ofPattern("dd_MM_yyyy"));
                    String currentTimeSuffix = "_hasta_" + LocalTime.now().format(DateTimeFormatter.ofPattern("HH_mm"));

                    filename = String.format("informe_diario_plaza_%d_sotano_%d_%s%s.pdf",
                            spot.getSpotNumber(), spot.getFloor().getFloorNumber(),
                            formattedDay, currentTimeSuffix);
                }
            }

            // Create response with PDF content
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", filename);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfOutput.toByteArray());

        } catch (Exception e) {
            logger.error("Error generating PDF report: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Calculates the consumption for today.
     *
     * @param spotId The ID of the spot.
     * @return The consumption for today.
     */
    private Float calculateTodayConsumption(Long spotId) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);
        return statisticsService.getTotalConsumption(spotId, startOfDay, endOfDay);
    }

    /**
     * Calculates the consumption for the current month.
     *
     * @param spotId The ID of the spot.
     * @return The consumption for the current month.
     */
    private Float calculateThisMonthConsumption(Long spotId) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfMonth = today.withDayOfMonth(1).atStartOfDay();
        LocalDateTime endOfMonth = today.atTime(LocalTime.MAX);
        return statisticsService.getTotalConsumption(spotId, startOfMonth, endOfMonth);
    }

    /**
     * Calculates the consumption for the current year.
     *
     * @param spotId The ID of the spot.
     * @return The consumption for the current year.
     */
    private Float calculateThisYearConsumption(Long spotId) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfYear = today.withDayOfMonth(1).withMonth(1).atStartOfDay();
        LocalDateTime endOfYear = today.atTime(LocalTime.MAX);
        return statisticsService.getTotalConsumption(spotId, startOfYear, endOfYear);
    }

    /**
     * Generates the hourly chart for the selected day.
     */
    private void generateHourlyChart(Model model, Long spotId, LocalDate selectedDay, boolean isCurrentDay,
                                     int chartWidth, int chartHeight, String yAxisLabel) {
        // Generate hourly chart for the selected day
        LocalDateTime dayStart = selectedDay.atStartOfDay();
        LocalDateTime dayEnd = isCurrentDay ? LocalDateTime.now() : selectedDay.atTime(LocalTime.MAX);

        Map<String, Float> hourlyData = statisticsService.getHourlyConsumption(spotId, dayStart, dayEnd);

        // If it's the current day, filter out future hours
        if (isCurrentDay) {
            int currentHour = LocalTime.now().getHour();
            Map<String, Float> filteredHourlyData = new LinkedHashMap<>();

            // Only include hours up to the current hour
            hourlyData.entrySet().stream()
                    .filter(entry -> {
                        // Extract the hour from the key (format: "HH:00 - HH:00")
                        String hourStr = entry.getKey().substring(0, 2);
                        int hour = Integer.parseInt(hourStr);
                        return hour <= currentHour;
                    })
                    .forEach(entry -> filteredHourlyData.put(entry.getKey(), entry.getValue()));

            hourlyData = filteredHourlyData;
        }

        String hourlyChartTitle = "Consumo por hora - " + statisticsService.getFormattedDayForTitle(selectedDay.toString());
        if (isCurrentDay) {
            hourlyChartTitle += " (hasta " + LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm")) + ")";
        }

        String hourlyBarChartSvg = statisticsService.generateBarChartSvg(hourlyData, chartWidth, chartHeight, hourlyChartTitle, yAxisLabel);
        model.addAttribute("hourlyBarChartSvg", hourlyBarChartSvg);
        model.addAttribute("hourlyData", hourlyData);
        model.addAttribute("isCurrentDay", isCurrentDay);
    }

    /**
     * Generates the daily chart for the selected month.
     */
    private void generateDailyChart(Model model, Long spotId, YearMonth yearMonth, boolean isCurrentMonth,
                                    int chartWidth, int chartHeight, String yAxisLabel) {
        // Generate daily chart for the selected month
        LocalDateTime monthStart = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime monthEnd = isCurrentMonth ? LocalDateTime.now() : yearMonth.atEndOfMonth().atTime(LocalTime.MAX);

        Map<String, Float> dailyData = statisticsService.getDailyConsumption(spotId, monthStart, monthEnd);

        // If it's the current month, filter out future days
        if (isCurrentMonth) {
            int currentDay = LocalDate.now().getDayOfMonth();
            Map<String, Float> filteredDailyData = new LinkedHashMap<>();

            // Only include days up to the current day
            dailyData.entrySet().stream()
                    .filter(entry -> {
                        // Extract the day from the key (format: "dd/MM")
                        String dayStr = entry.getKey().substring(0, 2);
                        int day = Integer.parseInt(dayStr);
                        return day <= currentDay;
                    })
                    .forEach(entry -> filteredDailyData.put(entry.getKey(), entry.getValue()));

            dailyData = filteredDailyData;
        }

        String dailyChartTitle = "Consumo diario - " + yearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.of("es", "ES")) + " " + yearMonth.getYear();
        if (isCurrentMonth) {
            dailyChartTitle += " (hasta " + LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM")) + ")";
        }

        String dailyBarChartSvg = statisticsService.generateBarChartSvg(dailyData, chartWidth, chartHeight, dailyChartTitle, yAxisLabel);
        model.addAttribute("dailyBarChartSvg", dailyBarChartSvg);
        model.addAttribute("dailyData", dailyData);
        model.addAttribute("isCurrentMonth", isCurrentMonth);
    }

    /**
     * Generates the monthly chart for the selected year.
     */
    private void generateMonthlyChart(Model model, Long spotId, Integer selectedYear, boolean isCurrentYear,
                                      int chartWidth, int chartHeight, String yAxisLabel) {
        // Generate monthly chart for the selected year
        // Always use the full year range to ensure all months are included
        LocalDateTime yearStart = LocalDate.of(selectedYear, 1, 1).atStartOfDay();
        LocalDateTime yearEnd = LocalDate.of(selectedYear, 12, 31).atTime(LocalTime.MAX);

        Map<String, Float> monthlyData = statisticsService.getMonthlyConsumption(spotId, yearStart, yearEnd);

        // If it's the current year, filter out future months
        if (isCurrentYear) {
            int currentMonth = LocalDate.now().getMonthValue();
            Map<String, Float> filteredMonthlyData = new LinkedHashMap<>();

            // Only include months up to the current month
            // The keys are month names in Spanish, so we need to map them to month numbers
            Map<String, Integer> monthNameToNumber = new HashMap<>();
            for (int i = 1; i <= 12; i++) {
                String monthName = Month.of(i).getDisplayName(TextStyle.SHORT, Locale.of("es", "ES"));
                // Capitalize first letter to match the format in the map
                monthName = monthName.substring(0, 1).toUpperCase() + monthName.substring(1);
                monthNameToNumber.put(monthName, i);
            }

            monthlyData.entrySet().stream()
                    .filter(entry -> {
                        Integer monthNumber = monthNameToNumber.get(entry.getKey());
                        return monthNumber != null && monthNumber <= currentMonth;
                    })
                    .forEach(entry -> filteredMonthlyData.put(entry.getKey(), entry.getValue()));

            monthlyData = filteredMonthlyData;
        }

        String monthlyChartTitle = "Consumo mensual - " + selectedYear;
        if (isCurrentYear) {
            YearMonth currentYearMonth = YearMonth.now();
            monthlyChartTitle += " (hasta " + currentYearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.of("es", "ES")) + ")";
        }

        String monthlyBarChartSvg = statisticsService.generateBarChartSvg(monthlyData, chartWidth, chartHeight, monthlyChartTitle, yAxisLabel);
        model.addAttribute("monthlyBarChartSvg", monthlyBarChartSvg);
        model.addAttribute("monthlyData", monthlyData);
        model.addAttribute("isCurrentYear", isCurrentYear);
    }

    /**
     * Adds date range attributes to the model for readings links.
     */
    private void addDateRangeAttributes(Model model, LocalDate selectedDay, boolean isCurrentDay,
                                        String selectedMonth, boolean isCurrentMonth,
                                        Integer selectedYear, boolean isCurrentYear) {
        // Daily readings date range
        LocalDateTime[] dailyDateRange = DateTimeUtils.calculateDailyDateTimeRange(selectedDay, isCurrentDay);
        model.addAttribute("dailyStartDateTime", dailyDateRange[0]);
        model.addAttribute("dailyEndDateTime", dailyDateRange[1]);

        // Monthly readings date range
        YearMonth yearMonthObj = YearMonth.parse(selectedMonth);
        LocalDateTime[] monthlyDateRange = DateTimeUtils.calculateMonthlyDateTimeRange(yearMonthObj, isCurrentMonth);
        model.addAttribute("monthlyStartDateTime", monthlyDateRange[0]);
        model.addAttribute("monthlyEndDateTime", monthlyDateRange[1]);

        // Yearly readings date range
        LocalDateTime[] yearlyDateRange = DateTimeUtils.calculateYearlyDateTimeRange(selectedYear, isCurrentYear);
        model.addAttribute("yearlyStartDateTime", yearlyDateRange[0]);
        model.addAttribute("yearlyEndDateTime", yearlyDateRange[1]);
    }

    /**
     * Determines if the request is coming from a mobile device based on the User-Agent header.
     *
     * @param request The HTTP request.
     * @return True if the request is from a mobile device, false otherwise.
     */
    private boolean isMobileDevice(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent").toLowerCase();
        return userAgent.contains("mobile") || userAgent.contains("android") || userAgent.contains("iphone") ||
                userAgent.contains("ipad") || userAgent.contains("ipod") || userAgent.contains("blackberry") ||
                userAgent.contains("windows phone");
    }

    /**
     * Checks if a spot has any readings in the database.
     *
     * @param spotId The ID of the spot to check
     * @return true if the spot has at least one reading, false otherwise
     */
    private boolean checkIfSpotHasAnyReadings(Long spotId) {
        // Get a very wide date range to check for any readings
        LocalDateTime veryOldDate = LocalDateTime.of(2000, 1, 1, 0, 0);
        LocalDateTime farFutureDate = LocalDateTime.of(2100, 12, 31, 23, 59, 59);

        // Use the statistics service to check if there are any readings
        Float totalConsumption = statisticsService.getTotalConsumption(spotId, veryOldDate, farFutureDate);

        // If there's any consumption at all, there must be readings
        return totalConsumption > 0;
    }
}
