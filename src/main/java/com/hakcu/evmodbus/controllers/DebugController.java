package com.hakcu.evmodbus.controllers;

import com.hakcu.evmodbus.components.ModbusRTU;
import com.hakcu.evmodbus.enums.Unit;
import com.hakcu.evmodbus.services.MailService;
import com.hakcu.evmodbus.services.ReportingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for debugging operations.
 * Provides endpoints for testing and debugging purposes.
 * These endpoints should not be exposed in production.
 */
@RestController
@RequestMapping("/debug")
public class DebugController {
    private static final Logger logger = LoggerFactory.getLogger(DebugController.class);

    private final ModbusRTU modbusRTU;
    private final ReportingService reportingService;
    private final MailService mailService;

    DebugController(ModbusRTU modbusRTU, ReportingService reportingService,
                    MailService mailService) {
        this.modbusRTU = modbusRTU;
        this.reportingService = reportingService;
        this.mailService = mailService;
    }

    /**
     * Gets information about available serial devices.
     */
    @GetMapping("/serial")
    public ResponseEntity<String[]> getSerialInfo() {
        try {
            String[] devices = modbusRTU.getDeviceList();
            return ResponseEntity.ok(devices);
        } catch (Exception e) {
            logger.error("Error getting serial info: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Tests reading and saving data from all meters.
     */
    @GetMapping("/test")
    public ResponseEntity<String> test() {
        try {
            reportingService.readAndSave(Unit.KWH);
            return ResponseEntity.ok("Test completed successfully");
        } catch (Exception e) {
            logger.error("Error during test: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Tests the email service.
     */
    @GetMapping("/email")
    public ResponseEntity<String> email() {
        try {
            mailService.sendMail("Probando servicio de correo electrónico");
            return ResponseEntity.ok("Mail sent successfully");
        } catch (Exception e) {
            logger.error("Error sending email: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error sending email: " + e.getMessage());
        }
    }

    /**
     * Gets information about available serial ports.
     */
    @GetMapping("/ports")
    public ResponseEntity<String> getPorts() {
        try {
            String portsInfo = modbusRTU.getPortsInfo();
            return ResponseEntity.ok(portsInfo);
        } catch (Exception e) {
            logger.error("Error getting ports info: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }
}
