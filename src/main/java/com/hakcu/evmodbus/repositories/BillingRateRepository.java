package com.hakcu.evmodbus.repositories;

import com.hakcu.evmodbus.entities.BillingRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BillingRateRepository extends JpaRepository<BillingRate, Long> {
    @Query("SELECT br FROM BillingRate br WHERE br.deletedAt IS NULL ORDER BY br.name ASC")
    List<BillingRate> findAllActive();

    @Query("SELECT br FROM BillingRate br WHERE br.id = :id AND br.deletedAt IS NULL")
    Optional<BillingRate> findActiveById(Long id);

    @Query("SELECT br FROM BillingRate br WHERE br.isDefault = true AND br.deletedAt IS NULL")
    Optional<BillingRate> findDefaultRate();
}
