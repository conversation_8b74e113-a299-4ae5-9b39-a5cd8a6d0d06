package com.hakcu.evmodbus.repositories;

import com.hakcu.evmodbus.entities.BillingItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BillingItemRepository extends JpaRepository<BillingItem, Long> {
    @Query("SELECT bi FROM BillingItem bi WHERE bi.deletedAt IS NULL ORDER BY bi.id")
    List<BillingItem> findAllActive();
    
    @Query("SELECT bi FROM BillingItem bi WHERE bi.billing.id = :billingId AND bi.deletedAt IS NULL ORDER BY bi.spot.spotNumber")
    List<BillingItem> findAllActiveByBillingId(@Param("billingId") Long billingId);
    
    @Query("SELECT bi FROM BillingItem bi WHERE bi.spot.id = :spotId AND bi.deletedAt IS NULL ORDER BY bi.billing.billingPeriod DESC")
    List<BillingItem> findAllActiveBySpotId(@Param("spotId") Long spotId);
}
