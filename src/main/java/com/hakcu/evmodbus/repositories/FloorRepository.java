package com.hakcu.evmodbus.repositories;

import com.hakcu.evmodbus.entities.Floor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;
import java.util.Optional;

public interface FloorRepository extends JpaRepository<Floor, Long> {
    @Query("SELECT f FROM Floor f WHERE f.deletedAt IS NULL ORDER BY f.floorNumber")
    List<Floor> findAllActive();

    @Query("SELECT f FROM Floor f WHERE f.id = :id AND f.deletedAt IS NULL")
    Optional<Floor> findActiveById(Long id);
}
