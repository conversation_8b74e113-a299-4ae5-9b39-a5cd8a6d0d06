package com.hakcu.evmodbus.repositories;

import com.hakcu.evmodbus.entities.Spot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface SpotRepository extends JpaRepository<Spot, Long> {
    @Query("SELECT s FROM Spot s WHERE s.deletedAt IS NULL ORDER BY s.floor.floorNumber, s.spotNumber")
    List<Spot> findAllActive();

    @Query("SELECT s FROM Spot s WHERE s.id = :id AND s.deletedAt IS NULL")
    Optional<Spot> findActiveById(Long id);
}
