package com.hakcu.evmodbus.repositories;

import com.hakcu.evmodbus.entities.Reading;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ReadingRepository extends JpaRepository<Reading, Long> {
    @Query("SELECT r FROM Reading r WHERE r.deletedAt IS NULL ORDER BY r.dateTime DESC")
    List<Reading> findAllActive();

    @Query("SELECT r FROM Reading r WHERE r.id = :id AND r.deletedAt IS NULL")
    Optional<Reading> findActiveById(@Param("id") Long id);

    @Query("SELECT r FROM Reading r WHERE r.spot.id = ?1 AND r.deletedAt IS NULL ORDER BY r.dateTime DESC")
    Page<Reading> findAllActiveBySpot(Long spotId, Pageable pageable);

    @Query("SELECT r FROM Reading r WHERE r.spot.id = ?1 AND r.deletedAt IS NULL ORDER BY r.dateTime DESC")
    List<Reading> findAllActiveBySpot(Long id);

    @Query("SELECT r FROM Reading r WHERE r.deletedAt IS NULL " +
            "AND (COALESCE(:spotId, NULL) IS NULL OR r.spot.id = :spotId) " +
            "AND (COALESCE(:success, NULL) IS NULL OR r.success = :success) " +
            "AND (COALESCE(:startDateTime, NULL) IS NULL OR r.dateTime >= :startDateTime) " +
            "AND (COALESCE(:endDateTime, NULL) IS NULL OR r.dateTime <= :endDateTime) " +
            "ORDER BY r.dateTime DESC")
    Page<Reading> findAllActiveWithFilters(
            @Param("spotId") Long spotId,
            @Param("success") Boolean success,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime,
            Pageable pageable);

    /**
     * Finds the latest successful reading for a spot.
     *
     * @param spotId The ID of the spot.
     * @return The latest successful reading, or empty if none exists.
     */
    @Query(value = "SELECT r FROM Reading r WHERE r.spot.id = :spotId AND r.success = true AND r.deletedAt IS NULL ORDER BY r.dateTime DESC LIMIT 1")
    Optional<Reading> findLatestSuccessfulReadingBySpotId(@Param("spotId") Long spotId);

    /**
     * Finds all successful readings for a spot within a date range.
     *
     * @param spotId The ID of the spot.
     * @param startDateTime Start of the date range.
     * @param endDateTime End of the date range.
     * @return List of readings.
     */
    @Query("SELECT r FROM Reading r WHERE r.spot.id = :spotId AND r.success = true AND r.reading >= 0 AND r.deletedAt IS NULL AND r.dateTime >= :startDateTime AND r.dateTime <= :endDateTime ORDER BY r.dateTime")
    List<Reading> findSuccessfulReadingsBySpotAndDateRange(
            @Param("spotId") Long spotId,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime);

    /**
     * Calculates the total consumption (max reading - min reading) for a specific spot within a date range.
     *
     * @param spotId The ID of the spot.
     * @param startDateTime Start of the date range.
     * @param endDateTime End of the date range.
     * @return The total consumption value.
     */
    @Query("SELECT MAX(r.reading) - MIN(r.reading) " +
            "FROM Reading r " +
            "WHERE r.spot.id = :spotId " +
            "AND r.success = true " +
            "AND r.deletedAt IS NULL " +
            "AND r.dateTime >= :startDateTime " +
            "AND r.dateTime <= :endDateTime")
    Float calculateTotalConsumptionBySpot(
            @Param("spotId") Long spotId,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime);
}
