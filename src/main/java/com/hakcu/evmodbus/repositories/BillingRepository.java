package com.hakcu.evmodbus.repositories;

import com.hakcu.evmodbus.entities.Billing;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.YearMonth;
import java.util.List;
import java.util.Optional;

@Repository
public interface BillingRepository extends JpaRepository<Billing, Long> {
    @Query("SELECT MAX(b.billNumber) FROM Billing b WHERE b.deletedAt IS NULL")
    Optional<Long> findMaxBillNumber();

    @Query("SELECT COUNT(b) > 0 FROM Billing b WHERE b.billNumber = :billNumber AND b.deletedAt IS NULL")
    boolean existsByBillNumber(@Param("billNumber") Long billNumber);

    @Query("SELECT b FROM Billing b WHERE b.deletedAt IS NULL ORDER BY b.billingPeriod DESC, b.customer.lastName ASC, b.customer.firstName ASC")
    List<Billing> findAllActive();

    @Query("SELECT b FROM Billing b WHERE b.id = :id AND b.deletedAt IS NULL")
    Optional<Billing> findActiveById(@Param("id") Long id);

    @Query("SELECT b FROM Billing b WHERE b.billNumber = :billNumber AND b.deletedAt IS NULL")
    Optional<Billing> findByBillNumber(@Param("billNumber") Long billNumber);

    @Query("SELECT b FROM Billing b WHERE b.customer.id = :customerId AND b.deletedAt IS NULL ORDER BY b.billingPeriod DESC")
    List<Billing> findAllActiveByCustomerId(@Param("customerId") Long customerId);

    @Query("SELECT b FROM Billing b WHERE b.billingPeriod = :period AND b.customer.id = :customerId AND b.deletedAt IS NULL")
    Optional<Billing> findByPeriodAndCustomerId(@Param("period") YearMonth period, @Param("customerId") Long customerId);

    @Query(value = "SELECT b FROM Billing b WHERE b.deletedAt IS NULL " +
            "AND (COALESCE(:customerId, NULL) IS NULL OR b.customer.id = :customerId) " +
            "AND (COALESCE(:period, NULL) IS NULL OR b.billingPeriod = :period) " +
            "AND (COALESCE(:paid, NULL) IS NULL OR (:paid = TRUE AND b.status = 'PAID') OR (:paid = FALSE AND b.status IN ('DRAFT', 'PENDING'))) " +
            "AND (COALESCE(:billNumber, NULL) IS NULL OR b.billNumber = :billNumber) " +
            "ORDER BY b.billNumber DESC",
            countQuery = "SELECT COUNT(b) FROM Billing b WHERE b.deletedAt IS NULL " +
            "AND (COALESCE(:customerId, NULL) IS NULL OR b.customer.id = :customerId) " +
            "AND (COALESCE(:period, NULL) IS NULL OR b.billingPeriod = :period) " +
            "AND (COALESCE(:paid, NULL) IS NULL OR (:paid = TRUE AND b.status = 'PAID') OR (:paid = FALSE AND b.status IN ('DRAFT', 'PENDING'))) " +
            "AND (COALESCE(:billNumber, NULL) IS NULL OR b.billNumber = :billNumber) ")
    Page<Billing> findAllActiveWithFilters(
            @Param("customerId") Long customerId,
            @Param("period") YearMonth period,
            @Param("paid") Boolean paid,
            @Param("billNumber") Long billNumber,
            Pageable pageable);
}
