package com.hakcu.evmodbus.entities;

import jakarta.persistence.*;
import org.hibernate.annotations.SQLRestriction;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing an individual line item in a billing record.
 * Each item corresponds to a specific spot's consumption for the billing period.
 */
@Entity
@SQLRestriction("deleted_at IS NULL")
public class BillingItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "billing_id", nullable = false)
    private Billing billing;

    @ManyToOne
    private Spot spot;

    private Float consumption;

    @Column(precision = 10, scale = 4)
    private BigDecimal amount;

    @Column(precision = 10, scale = 4)
    private BigDecimal rateApplied;

    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime deletedAt;

    public BillingItem() {
    }

    public BillingItem(Spot spot, Float consumption, BigDecimal rateApplied) {
        this.spot = spot;
        this.consumption = consumption;
        this.rateApplied = rateApplied;
        this.amount = rateApplied.multiply(BigDecimal.valueOf(consumption));
    }

    /**
     * Constructor that also sets the billing relationship.
     *
     * @param spot The spot associated with this item
     * @param consumption The consumption value
     * @param rateApplied The rate applied
     * @param billing The billing this item belongs to
     */
    public BillingItem(Spot spot, Float consumption, BigDecimal rateApplied, Billing billing) {
        this(spot, consumption, rateApplied);
        this.billing = billing;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Billing getBilling() {
        return billing;
    }

    public void setBilling(Billing billing) {
        this.billing = billing;
    }

    public Spot getSpot() {
        return spot;
    }

    public void setSpot(Spot spot) {
        this.spot = spot;
    }

    public Float getConsumption() {
        return consumption;
    }

    public void setConsumption(Float consumption) {
        this.consumption = consumption;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getRateApplied() {
        return rateApplied;
    }

    public void setRateApplied(BigDecimal rateApplied) {
        this.rateApplied = rateApplied;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }
}
