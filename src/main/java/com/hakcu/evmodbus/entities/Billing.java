package com.hakcu.evmodbus.entities;

import com.hakcu.evmodbus.enums.BillingStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.SQLRestriction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a monthly billing record for a customer.
 */
@Entity
@SQLRestriction("deleted_at IS NULL")
public class Billing {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "bill_number", nullable = false)
    @NotNull(message = "El número de factura no puede estar vacío")
    @Min(value = 1, message = "El número de factura debe ser mayor que cero")
    private Long billNumber;

    @ManyToOne
    private Customer customer;

    private YearMonth billingPeriod;

    private LocalDate issueDate;

    private LocalDate dueDate;

    @Column(precision = 10, scale = 4)
    private BigDecimal totalAmount;

    @Column(precision = 10, scale = 4)
    private BigDecimal fixedFeeAmount;

    @Column(precision = 10, scale = 4)
    private BigDecimal consumptionAmount;

    @Column(precision = 10, scale = 4)
    private BigDecimal taxAmount;

    @Column(precision = 10, scale = 4)
    private BigDecimal discountAmount;

    private Float totalConsumption;

    @Column(columnDefinition = "TEXT")
    private String comments;

    @ManyToOne
    private BillingRate billingRate;

    @Enumerated(EnumType.STRING)
    private BillingStatus status;

    private LocalDate paidDate;

    @OneToMany(mappedBy = "billing", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<BillingItem> items = new ArrayList<>();

    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime deletedAt;

    public Billing() {
    }

    public Billing(Customer customer, YearMonth billingPeriod) {
        this.customer = customer;
        this.billingPeriod = billingPeriod;
        this.issueDate = LocalDate.now();
        this.dueDate = issueDate.plusDays(30); // Default due date is 30 days after issue
        this.status = BillingStatus.DRAFT;
        this.discountAmount = BigDecimal.ZERO;
        // billNumber will be set by the service
    }

    public Billing(Customer customer, YearMonth billingPeriod, Long billNumber) {
        this(customer, billingPeriod);
        this.billNumber = billNumber;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(Long billNumber) {
        this.billNumber = billNumber;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public YearMonth getBillingPeriod() {
        return billingPeriod;
    }

    public void setBillingPeriod(YearMonth billingPeriod) {
        this.billingPeriod = billingPeriod;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getFixedFeeAmount() {
        return fixedFeeAmount;
    }

    public void setFixedFeeAmount(BigDecimal fixedFeeAmount) {
        this.fixedFeeAmount = fixedFeeAmount;
    }

    public BigDecimal getConsumptionAmount() {
        return consumptionAmount;
    }

    public void setConsumptionAmount(BigDecimal consumptionAmount) {
        this.consumptionAmount = consumptionAmount;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Float getTotalConsumption() {
        return totalConsumption;
    }

    public void setTotalConsumption(Float totalConsumption) {
        this.totalConsumption = totalConsumption;
    }

    public BillingRate getBillingRate() {
        return billingRate;
    }

    public void setBillingRate(BillingRate billingRate) {
        this.billingRate = billingRate;
    }

    public BillingStatus getStatus() {
        return status;
    }

    public void setStatus(BillingStatus status) {
        this.status = status;
    }

    public boolean isPaid() {
        return status == BillingStatus.PAID;
    }

    public LocalDate getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(LocalDate paidDate) {
        this.paidDate = paidDate;
    }

    public List<BillingItem> getItems() {
        return items;
    }

    public void setItems(List<BillingItem> items) {
        this.items = items;
    }

    public void addItem(BillingItem item) {
        if (item == null) {
            return;
        }

        // Set the bidirectional relationship
        if (items == null) {
            items = new ArrayList<>();
        }

        items.add(item);
        item.setBilling(this);
    }

    public void removeItem(BillingItem item) {
        items.remove(item);
        item.setBilling(null);
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
