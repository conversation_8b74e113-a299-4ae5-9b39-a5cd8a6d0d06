package com.hakcu.evmodbus.entities;

import jakarta.persistence.*;
import org.hibernate.annotations.SQLRestriction;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing a billing rate configuration.
 * This allows for different rates to be applied to different customers or usage patterns.
 */
@Entity
@SQLRestriction("deleted_at IS NULL")
public class BillingRate {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String name;
    private String description;
    
    @Column(precision = 10, scale = 4)
    private BigDecimal ratePerKwh;
    
    @Column(precision = 10, scale = 4)
    private BigDecimal fixedMonthlyFee;
    
    private boolean isDefault;
    
    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime deletedAt;

    public BillingRate() {
    }

    public BillingRate(String name, BigDecimal ratePerKwh, BigDecimal fixedMonthlyFee) {
        this.name = name;
        this.ratePerKwh = ratePerKwh;
        this.fixedMonthlyFee = fixedMonthlyFee;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getRatePerKwh() {
        return ratePerKwh;
    }

    public void setRatePerKwh(BigDecimal ratePerKwh) {
        this.ratePerKwh = ratePerKwh;
    }

    public BigDecimal getFixedMonthlyFee() {
        return fixedMonthlyFee;
    }

    public void setFixedMonthlyFee(BigDecimal fixedMonthlyFee) {
        this.fixedMonthlyFee = fixedMonthlyFee;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }
}
