package com.hakcu.evmodbus.security.controllers;

import com.hakcu.evmodbus.security.entities.User;
import com.hakcu.evmodbus.security.repositories.RoleRepository;
import com.hakcu.evmodbus.security.services.UserService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;
import java.util.Optional;

/**
 * Controller for admin panel operations.
 */
@Controller
@RequestMapping("/admin")
@Secured("ROLE_ADMIN")
public class AdminController {

    private final UserService userService;
    private final RoleRepository roleRepository;

    public AdminController(UserService userService, RoleRepository roleRepository) {
        this.userService = userService;
        this.roleRepository = roleRepository;
    }

    /**
     * Displays the admin dashboard.
     *
     * @param model the model
     * @return the admin dashboard view
     */
    @GetMapping
    public String adminDashboard(Model model) {
        model.addAttribute("users", userService.findAll());
        return "admin/dashboard";
    }

    /**
     * Displays the user management page.
     *
     * @param model the model
     * @return the user management view
     */
    @GetMapping("/users")
    public String listUsers(Model model) {
        List<User> users = userService.findAll();
        model.addAttribute("users", users);
        return "admin/users/list";
    }

    /**
     * Displays the form to create a new user.
     *
     * @param model the model
     * @return the user form view
     */
    @GetMapping("/users/new")
    public String newUserForm(Model model) {
        model.addAttribute("user", new User());
        model.addAttribute("roles", roleRepository.findAll());
        model.addAttribute("selectedRole", null);
        return "admin/users/form";
    }

    /**
     * Displays the form to edit an existing user.
     *
     * @param id the user ID
     * @param model the model
     * @return the user form view
     */
    @GetMapping("/users/{id}/edit")
    public String editUserForm(@PathVariable Long id, Model model) {
        User user = userService.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("User not found with ID: " + id));

        model.addAttribute("user", user);
        model.addAttribute("roles", roleRepository.findAll());
        model.addAttribute("selectedRole", user.getRole());
        return "admin/users/form";
    }

    /**
     * Creates or updates a user.
     *
     * @param user the user data
     * @param redirectAttributes the redirect attributes
     * @return a redirect to the user list
     */
    @PostMapping("/users")
    public String saveUser(@ModelAttribute User user,
                          @RequestParam(required = false) Long roleId,
                          RedirectAttributes redirectAttributes) {

        // Get the current authenticated user
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = auth.getName();

        // Check if this is an update operation
        boolean isUpdate = user.getId() != null;

        // For updates, prevent self-disabling
        if (isUpdate && userService.findById(user.getId()).isPresent()) {
            User existingUser = userService.findById(user.getId()).get();
            if (existingUser.getUsername().equals(currentUsername) && !user.isEnabled()) {
                redirectAttributes.addFlashAttribute("errorMessage",
                    "No puedes desactivar tu propia cuenta");
                return "redirect:/admin/users";
            }
        }

        // Check if username already exists (case-insensitive)
        // Note: The username is automatically converted to lowercase in the User entity
        if (!isUpdate) {
            // For new users, check if username already exists
            if (userService.findByUsername(user.getUsername()).isPresent()) {
                redirectAttributes.addFlashAttribute("errorMessage",
                    "El nombre de usuario ya existe");
                return "redirect:/admin/users";
            }
        } else {
            // For updates, check if the new username exists but belongs to a different user
            Optional<User> existingUserWithSameUsername = userService.findByUsername(user.getUsername());
            if (existingUserWithSameUsername.isPresent() &&
                !existingUserWithSameUsername.get().getId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("errorMessage",
                    "El nombre de usuario ya existe");
                return "redirect:/admin/users";
            }
        }

        // Create or update the user
        if (isUpdate) {
            user = userService.update(user.getId(), user);
            if (roleId != null) {
                userService.setRoleForUser(user.getId(), roleId);
            }
            redirectAttributes.addFlashAttribute("successMessage",
                "Usuario actualizado correctamente");
        } else {
            user = userService.create(user);
            if (roleId != null) {
                userService.setRoleForUser(user.getId(), roleId);
            }
            redirectAttributes.addFlashAttribute("successMessage",
                "Usuario creado correctamente");
        }

        return "redirect:/admin/users";
    }

    /**
     * Deletes a user.
     *
     * @param id the user ID
     * @param redirectAttributes the redirect attributes
     * @return a redirect to the user list
     */
    @GetMapping("/users/{id}/delete")
    public String deleteUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        // Get the current authenticated user
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = auth.getName();

        // Check if the user is trying to delete themselves
        if (userService.findById(id).isPresent()) {
            User userToDelete = userService.findById(id).get();
            if (userToDelete.getUsername().equals(currentUsername)) {
                redirectAttributes.addFlashAttribute("errorMessage",
                    "No puedes eliminar tu propia cuenta");
                return "redirect:/admin/users";
            }
        }

        userService.delete(id);
        redirectAttributes.addFlashAttribute("successMessage", "Usuario eliminado correctamente");
        return "redirect:/admin/users";
    }
}
