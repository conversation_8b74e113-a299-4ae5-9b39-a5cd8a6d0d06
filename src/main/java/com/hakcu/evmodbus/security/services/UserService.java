package com.hakcu.evmodbus.security.services;

import com.hakcu.evmodbus.security.entities.Role;
import com.hakcu.evmodbus.security.entities.User;
import com.hakcu.evmodbus.security.repositories.RoleRepository;
import com.hakcu.evmodbus.security.repositories.UserRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for user management operations.
 */
@Service
public class UserService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    public UserService(UserRepository userRepository, RoleRepository roleRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * Find all users.
     *
     * @return a list of all users
     */
    @Transactional(readOnly = true)
    public List<User> findAll() {
        return userRepository.findAllActive();
    }

    /**
     * Find a user by ID.
     *
     * @param id the user ID
     * @return an Optional containing the user if found
     */
    @Transactional(readOnly = true)
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * Find a user by username (case-insensitive).
     * The username is automatically converted to lowercase for case-insensitive comparison.
     *
     * @param username the username
     * @return an Optional containing the user if found
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        // Username is converted to lowercase in the repository query
        return userRepository.findByUsername(username);
    }

    /**
     * Create a new user.
     *
     * @param user the user to create
     * @return the created user
     */
    @Transactional
    public User create(User user) {
        // Encode the password
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // Save the user
        return userRepository.save(user);
    }

    /**
     * Update an existing user.
     *
     * @param id the user ID
     * @param updatedUser the updated user data
     * @return the updated user
     */
    @Transactional
    public User update(Long id, User updatedUser) {
        User existingUser = userRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("User not found with ID: " + id));

        // Update fields
        existingUser.setEnabled(updatedUser.isEnabled());

        // Update password if provided
        if (updatedUser.getPassword() != null && !updatedUser.getPassword().isEmpty()) {
            existingUser.setPassword(passwordEncoder.encode(updatedUser.getPassword()));
        }

        // Save the updated user
        return userRepository.save(existingUser);
    }

    /**
     * Soft delete a user by ID.
     *
     * @param id the user ID
     * @return the deleted user
     */
    @Transactional
    public User delete(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("User not found with ID: " + id));

        user.setDeletedAt(LocalDateTime.now());
        return userRepository.save(user);
    }

    /**
     * Set role for a user.
     *
     * @param userId the user ID
     * @param roleId the role ID
     */
    @Transactional
    public void setRoleForUser(Long userId, Long roleId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with ID: " + userId));

        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new EntityNotFoundException("Role not found with ID: " + roleId));

        user.setRole(role);
        userRepository.save(user);
    }
}
