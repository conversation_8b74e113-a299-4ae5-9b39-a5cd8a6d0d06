package com.hakcu.evmodbus.security.repositories;

import com.hakcu.evmodbus.security.entities.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository for Role entity operations.
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    /**
     * Find a role by name.
     *
     * @param name the role name to search for
     * @return an Optional containing the role if found
     */
    Optional<Role> findByName(String name);
    
    /**
     * Check if a role with the given name exists.
     *
     * @param name the role name to check
     * @return true if a role with the name exists, false otherwise
     */
    boolean existsByName(String name);
}
