package com.hakcu.evmodbus.security.repositories;

import com.hakcu.evmodbus.security.entities.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for User entity operations.
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * Find a user by username (case-insensitive).
     * Note: The username is converted to lowercase before the query is executed,
     * as all usernames are stored in lowercase in the database.
     *
     * @param username the username to search for
     * @return an Optional containing the user if found
     */
    @Query("SELECT u FROM User u WHERE u.username = LOWER(?1) AND u.deletedAt IS NULL")
    Optional<User> findByUsername(String username);

    /**
     * Check if a user with the given username exists (case-insensitive).
     * Note: The username is converted to lowercase before the check is performed,
     * as all usernames are stored in lowercase in the database.
     *
     * @param username the username to check
     * @return true if a user with the username exists, false otherwise
     */
    @Query("SELECT COUNT(u) > 0 FROM User u WHERE u.username = LOWER(?1)")
    boolean existsByUsername(String username);

    /**
     * Find all active (non-deleted) users.
     *
     * @return a list of all active users
     */
    @Query("SELECT u FROM User u WHERE u.deletedAt IS NULL ORDER BY u.username")
    List<User> findAllActive();

    /**
     * Check if a user with the given username exists and is not deleted (case-insensitive).
     * Note: The username is converted to lowercase before the check is performed,
     * as all usernames are stored in lowercase in the database.
     *
     * @param username the username to check
     * @return true if an active user with the username exists, false otherwise
     */
    @Query("SELECT COUNT(u) > 0 FROM User u WHERE u.username = LOWER(?1) AND u.deletedAt IS NULL")
    boolean existsByUsernameAndNotDeleted(String username);
}
