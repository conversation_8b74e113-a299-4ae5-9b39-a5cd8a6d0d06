package com.hakcu.evmodbus.security.config;

import com.hakcu.evmodbus.security.entities.Role;
import com.hakcu.evmodbus.security.entities.User;
import com.hakcu.evmodbus.security.repositories.RoleRepository;
import com.hakcu.evmodbus.security.repositories.UserRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Initializes security data (roles and admin user) on application startup.
 */
@Configuration
public class SecurityDataInitializer {

    /**
     * Creates a CommandLineRunner bean to initialize security data.
     *
     * @param roleRepository the role repository
     * @param userRepository the user repository
     * @param passwordEncoder the password encoder
     * @return the CommandLineRunner
     */
    @Bean
    public CommandLineRunner initSecurityData(
            RoleRepository roleRepository,
            UserRepository userRepository,
            PasswordEncoder passwordEncoder) {

        return args -> {
            // Create roles if they don't exist
            createRoleIfNotExists(roleRepository, "ADMIN");
            createRoleIfNotExists(roleRepository, "MANAGER");
            createRoleIfNotExists(roleRepository, "USER");

            // Create admin user if it doesn't exist (case-insensitive check)
            // Note: The username is stored in lowercase for case-insensitive authentication
            if (!userRepository.existsByUsername("admin")) {
                User adminUser = new User("admin", passwordEncoder.encode("admin"));
                adminUser.setEnabled(true);

                // Set ADMIN role to admin user
                Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();
                adminUser.setRole(adminRole);

                userRepository.save(adminUser);

                System.out.println("Created default admin user with username: admin and password: admin");
                System.out.println("IMPORTANT: Change the default admin password in production!");
            }
        };
    }

    /**
     * Creates a role if it doesn't exist.
     *
     * @param roleRepository the role repository
     * @param roleName the role name
     */
    private void createRoleIfNotExists(RoleRepository roleRepository, String roleName) {
        if (!roleRepository.existsByName(roleName)) {
            Role role = new Role(roleName);
            roleRepository.save(role);
        }
    }
}
