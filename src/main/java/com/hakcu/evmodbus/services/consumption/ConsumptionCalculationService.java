package com.hakcu.evmodbus.services.consumption;

import com.hakcu.evmodbus.entities.Reading;
import com.hakcu.evmodbus.enums.TimeUnit;
import com.hakcu.evmodbus.repositories.ReadingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for calculating consumption based on meter readings.
 * This service centralizes consumption calculation logic used by both
 * StatisticsService and BillingService.
 */
@Service
public class ConsumptionCalculationService {
    private static final Logger logger = LoggerFactory.getLogger(ConsumptionCalculationService.class);

    private final ReadingRepository readingRepository;

    public ConsumptionCalculationService(ReadingRepository readingRepository) {
        this.readingRepository = readingRepository;
    }

    /**
     * Calculates total consumption for a spot within a date range.
     * Uses the repository's direct calculation method first, then falls back to
     * calculating from the first and last readings if needed.
     *
     * @param spotId The ID of the spot
     * @param startDateTime Start of the date range
     * @param endDateTime End of the date range
     * @return The total consumption in kWh, or 0 if no valid readings exist
     */
    public float calculateTotalConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        try {
            // First check if there are any readings in this date range
            if (doesNotHaveReadingsInDateRange(spotId, startDateTime, endDateTime)) {
                logger.info("No readings found for spot {} in date range {} to {}", spotId, startDateTime, endDateTime);
                return 0.0f;
            }

            // Primary approach: Use the repository method to calculate consumption (MAX - MIN reading)
            Float consumption = readingRepository.calculateTotalConsumptionBySpot(spotId, startDateTime, endDateTime);

            // Return the calculated consumption or 0 if null
            if (consumption != null && consumption > 0) {
                return consumption;
            }

            // Fallback approach: Calculate using first and last readings
            List<Reading> readings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(spotId, startDateTime, endDateTime);
            if (readings.isEmpty()) {
                return 0.0f;
            }

            readings.sort(Comparator.comparing(Reading::getDateTime));

            Reading firstReading = readings.getFirst();
            Reading lastReading = readings.getLast();

            if (firstReading.getReading() >= 0 && lastReading.getReading() >= 0 && lastReading.getReading() > firstReading.getReading()) {
                return lastReading.getReading() - firstReading.getReading();
            }

            return 0.0f;
        } catch (Exception e) {
            logger.error("Error calculating total consumption for spot {}: {}", spotId, e.getMessage());
            return 0.0f;
        }
    }

    /**
     * Calculates consumption for a spot within a date range, broken down by time unit.
     *
     * @param spotId The ID of the spot
     * @param startDateTime Start of the date range
     * @param endDateTime End of the date range
     * @param timeUnit The time unit to use for the calculation
     * @return Map of period labels to consumption values
     */
    public Map<String, Float> calculateConsumptionByTimeUnit(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime, TimeUnit timeUnit) {
        try {
            logger.info("Calculating consumption for spot {} from {} to {} using time unit {}",
                    spotId, startDateTime, endDateTime, timeUnit);

            // Get readings with extended range to ensure we have readings at period boundaries
            LocalDateTime extendedStart = timeUnit.getExtendedStart(startDateTime);
            LocalDateTime extendedEnd = timeUnit.getExtendedEnd(endDateTime);

            logger.info("Extended date range: {} to {}", extendedStart, extendedEnd);

            List<Reading> readings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(spotId, extendedStart, extendedEnd);
            logger.info("Found {} readings in extended date range", readings.size());

            List<LocalDateTime> periods = timeUnit.generatePeriods(startDateTime, endDateTime);
            logger.info("Generated {} periods: {}", periods.size(),
                    periods.stream().map(timeUnit::formatPeriod).collect(Collectors.toList()));

            // Initialize consumption map with all periods set to zero
            Map<String, Float> consumptionMap = initializeConsumptionMap(periods, timeUnit);
            logger.info("Initialized consumption map with {} periods: {}", consumptionMap.size(), consumptionMap.keySet());

            if (readings.isEmpty()) {
                logger.warn("No readings found for spot {} in date range", spotId);
                return consumptionMap;
            }

            readings.sort(Comparator.comparing(Reading::getDateTime));
            logger.info("First reading: {} = {}, Last reading: {} = {}",
                    readings.getFirst().getDateTime(), readings.getFirst().getReading(),
                    readings.getLast().getDateTime(), readings.getLast().getReading());

            // Calculate consumption for each period
            for (LocalDateTime periodStart : periods) {
                LocalDateTime periodEnd = timeUnit.getEndOfPeriod(periodStart);
                String periodKey = timeUnit.formatPeriod(periodStart);

                logger.debug("Processing period: {} to {} ({})", periodStart, periodEnd, periodKey);

                // Try approach 1: Last reading of period and first reading of next period
                Reading lastReadingOfPeriod = getBoundaryReading(readings, periodStart, periodEnd, false);
                Reading firstReadingOfNextPeriod = getBoundaryReading(readings, periodEnd.plusNanos(1), timeUnit.getEndOfPeriod(periodEnd.plusNanos(1)), true);

                float consumption = calculateConsumption(lastReadingOfPeriod, firstReadingOfNextPeriod);

                // If approach 1 didn't work, try approach 2: First and last readings within the period
                if (consumption == 0.0f) {
                    logger.debug("Trying alternative approach for period {}", periodKey);
                    Reading firstReadingOfPeriod = getBoundaryReading(readings, periodStart, periodEnd, true);

                    if (firstReadingOfPeriod != null && lastReadingOfPeriod != null &&
                            !firstReadingOfPeriod.getId().equals(lastReadingOfPeriod.getId())) {
                        consumption = calculateConsumption(firstReadingOfPeriod, lastReadingOfPeriod);
                        logger.debug("Alternative approach result: {}", consumption);
                    }
                }

                // We no longer use interpolation approach since we want to show zero when there's no data

                consumptionMap.put(periodKey, consumption);
                logger.debug("Final consumption for period {}: {}", periodKey, consumption);
            }

            // Log the final consumption map
            logger.info("Final consumption map: {}", consumptionMap);

            return consumptionMap;
        } catch (Exception e) {
            logger.error("Error calculating consumption by time unit for spot {}: {}", spotId, e.getMessage(), e);
            return createEmptyConsumptionMap(startDateTime, endDateTime, timeUnit);
        }
    }

    /**
     * Calculates consumption for a specific month (for billing purposes).
     *
     * @param spotId The ID of the spot
     * @param period The billing period (year and month)
     * @return The total consumption in kWh
     */
    public float calculateMonthlyConsumption(Long spotId, YearMonth period) {
        LocalDateTime startDateTime = period.atDay(1).atStartOfDay();
        LocalDateTime endDateTime = period.atEndOfMonth().atTime(23, 59, 59);

        // First check if there are any readings in this date range
        if (doesNotHaveReadingsInDateRange(spotId, startDateTime, endDateTime)) {
            logger.info("No readings found for spot {} in period {}", spotId, period);
            return 0.0f;
        }

        return calculateTotalConsumption(spotId, startDateTime, endDateTime);
    }

    /**
     * Gets readings for a spot within an extended date range based on the time unit.
     *
     * @param spotId The ID of the spot
     * @param start Start of the date range
     * @param end End of the date range
     * @param timeUnit The time unit to use for extending the range
     * @return List of readings
     */
    private List<Reading> getReadingsWithExtendedRange(Long spotId, LocalDateTime start, LocalDateTime end, TimeUnit timeUnit) {
        LocalDateTime extendedStart = timeUnit.getExtendedStart(start);
        LocalDateTime extendedEnd = timeUnit.getExtendedEnd(end);
        logger.debug("Getting readings with extended range: {} to {}", extendedStart, extendedEnd);
        List<Reading> readings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(spotId, extendedStart, extendedEnd);
        logger.debug("Found {} readings in extended range", readings.size());
        return readings;
    }

    /**
     * Gets a boundary reading from a list of readings.
     *
     * @param readings List of readings
     * @param start Start of the period
     * @param end End of the period
     * @param findFirst Whether to find the first or last reading in the period
     * @return The boundary reading, or null if none exists
     */
    private Reading getBoundaryReading(List<Reading> readings, LocalDateTime start, LocalDateTime end, boolean findFirst) {
        Comparator<Reading> comparator = findFirst ? Comparator.comparing(Reading::getDateTime) : Comparator.comparing(Reading::getDateTime).reversed();

        // Log the boundary parameters
        logger.debug("Finding {} reading between {} and {}", findFirst ? "first" : "last", start, end);

        // Filter readings within the time range
        List<Reading> filteredReadings = readings.stream()
                .filter(r -> !r.getDateTime().isBefore(start) && !r.getDateTime().isAfter(end))
                .filter(r -> r.getReading() >= 0)
                .collect(Collectors.toList());

        logger.debug("Found {} readings in range", filteredReadings.size());

        if (!filteredReadings.isEmpty()) {
            // Sort and get the first/last reading
            filteredReadings.sort(comparator);
            Reading result = filteredReadings.getFirst();
            logger.debug("Selected reading: {} = {}", result.getDateTime(), result.getReading());
            return result;
        }

        logger.debug("No readings found in range");
        return null;
    }

    /**
     * Calculates consumption between two readings.
     *
     * @param startReading The start reading
     * @param endReading The end reading
     * @return The consumption value
     */
    private float calculateConsumption(Reading startReading, Reading endReading) {
        if (startReading != null && endReading != null) {
            // Normal case: end reading is greater than start reading
            if (endReading.getReading() > startReading.getReading()) {
                float consumption = endReading.getReading() - startReading.getReading();
                logger.debug("Calculated consumption: {} - {} = {}",
                        endReading.getReading(), startReading.getReading(), consumption);
                return consumption;
            }
            // Special case: meter reset (end reading is much smaller than start reading)
            else if (startReading.getReading() > 1000 && endReading.getReading() < 100) {
                logger.debug("Possible meter reset detected: {} -> {}",
                        startReading.getReading(), endReading.getReading());
                // Assume the meter reset to zero and started counting again
                return endReading.getReading();
            }
            else {
                logger.debug("End reading ({}) is not greater than start reading ({})",
                        endReading.getReading(), startReading.getReading());
            }
        } else {
            logger.debug("Cannot calculate consumption: startReading={}, endReading={}",
                    startReading != null ? startReading.getReading() : "null",
                    endReading != null ? endReading.getReading() : "null");
        }
        return 0.0f;
    }

    /**
     * Initializes a consumption map with zero values.
     *
     * @param periods List of period start times
     * @param timeUnit The time unit to use for formatting period labels
     * @return Map of period labels to zero values
     */
    private Map<String, Float> initializeConsumptionMap(List<LocalDateTime> periods, TimeUnit timeUnit) {
        return periods.stream()
                .collect(Collectors.toMap(
                        timeUnit::formatPeriod,
                        k -> 0.0f,
                        (a, b) -> a,
                        LinkedHashMap::new
                ));
    }

    /**
     * Creates an empty consumption map for a date range.
     *
     * @param start Start of the date range
     * @param end End of the date range
     * @param timeUnit The time unit to use for generating periods
     * @return Map of period labels to zero values
     */
    private Map<String, Float> createEmptyConsumptionMap(LocalDateTime start, LocalDateTime end, TimeUnit timeUnit) {
        List<LocalDateTime> periods = timeUnit.generatePeriods(start, end);
        return initializeConsumptionMap(periods, timeUnit);
    }

    /**
     * Gets hourly consumption for a spot within a date range.
     *
     * @param spotId The ID of the spot
     * @param startDateTime Start of the date range
     * @param endDateTime End of the date range
     * @return Map of hour labels to consumption values
     */
    public Map<String, Float> getHourlyConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        logger.info("Getting hourly consumption for spot {} from {} to {}", spotId, startDateTime, endDateTime);

        // First check if there are any readings in this date range
        if (doesNotHaveReadingsInDateRange(spotId, startDateTime, endDateTime)) {
            logger.info("No readings found for spot {} in date range {} to {}", spotId, startDateTime, endDateTime);
            // Return map with zero values for all hours
            return createEmptyConsumptionMap(startDateTime, endDateTime, TimeUnit.HOUR);
        }

        return calculateConsumptionByTimeUnit(spotId, startDateTime, endDateTime, TimeUnit.HOUR);
    }

    /**
     * Gets daily consumption for a spot within a date range.
     *
     * @param spotId The ID of the spot
     * @param startDateTime Start of the date range
     * @param endDateTime End of the date range
     * @return Map of day labels to consumption values
     */
    public Map<String, Float> getDailyConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        logger.info("Getting daily consumption for spot {} from {} to {}", spotId, startDateTime, endDateTime);

        // First check if there are any readings in this date range
        if (doesNotHaveReadingsInDateRange(spotId, startDateTime, endDateTime)) {
            logger.info("No readings found for spot {} in date range {} to {}", spotId, startDateTime, endDateTime);
            // Return map with zero values for all days
            return createEmptyConsumptionMap(startDateTime, endDateTime, TimeUnit.DAY);
        }

        return calculateConsumptionByTimeUnit(spotId, startDateTime, endDateTime, TimeUnit.DAY);
    }

    /**
     * Gets monthly consumption for a spot within a date range.
     *
     * @param spotId The ID of the spot
     * @param startDateTime Start of the date range
     * @param endDateTime End of the date range
     * @return Map of month labels to consumption values
     */
    public Map<String, Float> getMonthlyConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        logger.info("Getting monthly consumption for spot {} from {} to {}", spotId, startDateTime, endDateTime);

        // First check if there are any readings in this date range
        if (doesNotHaveReadingsInDateRange(spotId, startDateTime, endDateTime)) {
            logger.info("No readings found for spot {} in date range {} to {}", spotId, startDateTime, endDateTime);
            // Return map with zero values for all months
            Map<String, Float> emptyMap = createEmptyConsumptionMap(startDateTime, endDateTime, TimeUnit.MONTH);

            // For yearly view, ensure all months are included
            if (startDateTime.getMonthValue() == 1 &&
                (endDateTime.getMonthValue() == 12 || endDateTime.getYear() > startDateTime.getYear())) {

                logger.info("Ensuring all months are included in yearly view");
                ensureAllMonthsIncluded(emptyMap, startDateTime.getYear());
            }

            return emptyMap;
        }

        Map<String, Float> result = calculateConsumptionByTimeUnit(spotId, startDateTime, endDateTime, TimeUnit.MONTH);

        // For yearly view, ensure all months are included
        if (startDateTime.getMonthValue() == 1 &&
            (endDateTime.getMonthValue() == 12 || endDateTime.getYear() > startDateTime.getYear())) {

            logger.info("Ensuring all months are included in yearly view");
            ensureAllMonthsIncluded(result, startDateTime.getYear());
        }

        return result;
    }

    /**
     * Distributes consumption evenly across all periods.
     *
     * @param consumptionMap The map to update
     * @param totalConsumption The total consumption to distribute
     */
    private void distributeConsumptionEvenly(Map<String, Float> consumptionMap, float totalConsumption) {
        if (consumptionMap.isEmpty()) return;

        float valuePerPeriod = totalConsumption / consumptionMap.size();
        logger.info("Distributing total consumption {} evenly: {} per period", totalConsumption, valuePerPeriod);

        consumptionMap.replaceAll((k, v) -> valuePerPeriod);
    }

    /**
     * Distributes consumption based on typical usage patterns.
     *
     * @param consumptionMap The map to update
     * @param totalConsumption The total consumption to distribute
     * @param patternType The type of pattern to use (hourly, daily, monthly)
     */
    private void distributeConsumptionByPattern(Map<String, Float> consumptionMap, float totalConsumption, String patternType) {
        if (consumptionMap.isEmpty()) return;

        // Simple pattern for hourly consumption (higher during day, lower at night)
        if ("hourly".equals(patternType)) {
            float totalWeight = 0;
            Map<String, Float> weights = new HashMap<>();

            for (String hourKey : consumptionMap.keySet()) {
                // Extract hour from format like "08:00 - 09:00"
                int hour = Integer.parseInt(hourKey.substring(0, 2));

                // Assign weights based on time of day
                float weight;
                if (hour >= 8 && hour <= 20) {  // Daytime (8am-8pm)
                    weight = 1.5f;  // Higher weight during day
                } else {  // Nighttime
                    weight = 0.5f;  // Lower weight at night
                }

                weights.put(hourKey, weight);
                totalWeight += weight;
            }

            // Distribute consumption based on weights
            for (String key : consumptionMap.keySet()) {
                float proportion = weights.get(key) / totalWeight;
                float value = totalConsumption * proportion;
                consumptionMap.put(key, value);
                logger.debug("Assigned {} consumption to hour {}", value, key);
            }
        } else {
            // Default to even distribution for other pattern types
            distributeConsumptionEvenly(consumptionMap, totalConsumption);
        }
    }

    /**
     * Distributes consumption based on the number of days in each month.
     *
     * @param consumptionMap The map to update
     * @param totalConsumption The total consumption to distribute
     * @param year The year (for determining days in month)
     */
    private void distributeConsumptionByDaysInMonth(Map<String, Float> consumptionMap, float totalConsumption, int year) {
        if (consumptionMap.isEmpty()) return;

        int totalDays = 0;
        Map<String, Integer> daysInMonth = new HashMap<>();

        // Calculate days in each month
        for (String monthKey : consumptionMap.keySet()) {
            // Parse month from Spanish abbreviation (e.g., "Ene" for January)
            int monthNumber = getMonthNumberFromSpanishAbbreviation(monthKey);

            int days = YearMonth.of(year, monthNumber).lengthOfMonth();
            daysInMonth.put(monthKey, days);
            totalDays += days;
        }

        // Distribute consumption based on days in month
        for (String key : consumptionMap.keySet()) {
            int days = daysInMonth.get(key);
            float proportion = (float) days / totalDays;
            float value = totalConsumption * proportion;
            consumptionMap.put(key, value);
            logger.debug("Assigned {} consumption to month {} ({} days)", value, key, days);
        }
    }

    /**
     * Ensures all months are included in the yearly consumption map.
     * If a month is missing, it will be added with a zero value.
     *
     * @param consumptionMap The consumption map to update
     * @param year The year for which to ensure all months
     */
    private void ensureAllMonthsIncluded(Map<String, Float> consumptionMap, int year) {
        // List of all month abbreviations in Spanish
        String[] allMonthsSpanish = {
            "Ene", "Feb", "Mar", "Abr", "May", "Jun",
            "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"
        };

        // Create a new map to preserve order
        Map<String, Float> orderedMap = new LinkedHashMap<>();

        // Add all months in order
        for (String monthAbbr : allMonthsSpanish) {
            // If the month exists in the original map, use its value, otherwise use 0
            float value = consumptionMap.getOrDefault(monthAbbr, 0.0f);
            orderedMap.put(monthAbbr, value);

            if (!consumptionMap.containsKey(monthAbbr)) {
                logger.debug("Added missing month {} with zero value", monthAbbr);
            }
        }

        // Clear and update the original map
        consumptionMap.clear();
        consumptionMap.putAll(orderedMap);

        logger.info("Final consumption map with all months: {}", consumptionMap);
    }

    /**
     * Converts a Spanish month abbreviation to its numeric value (1-12).
     *
     * @param monthKey The Spanish month abbreviation (e.g., "Ene" for January)
     * @return The month number (1-12)
     */
    private int getMonthNumberFromSpanishAbbreviation(String monthKey) {
        return switch (monthKey.toLowerCase()) {
            case "ene" -> 1;
            case "feb" -> 2;
            case "mar" -> 3;
            case "abr" -> 4;
            case "may" -> 5;
            case "jun" -> 6;
            case "jul" -> 7;
            case "ago" -> 8;
            case "sep" -> 9;
            case "oct" -> 10;
            case "nov" -> 11;
            case "dic" -> 12;
            default -> 1; // Default to January if unknown
        };
    }

    /**
     * Checks if there are any readings for a spot within a date range.
     *
     * @param spotId The ID of the spot
     * @param startDateTime Start of the date range
     * @param endDateTime End of the date range
     * @return true if there are readings, false otherwise
     */
    private boolean doesNotHaveReadingsInDateRange(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        List<Reading> readings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(spotId, startDateTime, endDateTime);
        return readings.isEmpty();
    }
}
