package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Floor;
import com.hakcu.evmodbus.entities.Reading;
import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.enums.Unit;
import com.hakcu.evmodbus.records.FloorReading;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for orchestrating Modbus readings, saving results, and reporting errors.
 * Handles retries, error notification, and aggregation of readings.
 */
@Service
@Transactional
public class ReportingService {

    private static final Logger logger = LoggerFactory.getLogger(ReportingService.class);
    private static final int NUMBER_OF_RETRIES = 3;
    private final ReadingService readingService;
    private final MailService mailService;
    private final FloorReaderService floorReaderService;
    private final FloorService floorService;

    ReportingService(ReadingService readingService, MailService mailService,
                     FloorReaderService floorReaderService,
                     FloorService floorService) {
        this.readingService = readingService;
        this.mailService = mailService;
        this.floorReaderService = floorReaderService;
        this.floorService = floorService;
    }

    /**
     * Reads all floors and saves the readings, retrying on failure.
     * Sends an email if any floor fails after retries.
     *
     * @param unit The unit to read.
     * @throws InterruptedException If interrupted during reading.
     */
    public void readAndSave(Unit unit) throws InterruptedException {
        List<Integer> failedFloors = new ArrayList<>();
        List<Reading> allReadings = new ArrayList<>();
        for (Floor floor : floorService.findAll()) {
            boolean success = tryReadingFloor(floor, allReadings, unit);
            if (!success) {
                logger.warn("Adding failed registers from floor {}", floor.getFloorNumber());
                failedFloors.add(floor.getFloorNumber());
            }
        }
        if (!failedFloors.isEmpty()) {
            logger.warn("System will send an email for failed floors: {}", failedFloors);
            //mailService.sendMail("Error en las siguientes plantas: " + failedFloors + "\n");
        }
    }

    /**
     * Reads a single spot without saving the result.
     *
     * @param spot The spot to read.
     * @param unit The unit to read.
     * @return The reading.
     */
    public Reading readSpotWithoutSaving(Spot spot, Unit unit) {
        return floorReaderService.readSpot(spot, unit);
    }

    /**
     * Enqueues a spot reading asynchronously.
     *
     * @param spot The spot to read.
     * @param unit The unit to read.
     * @return CompletableFuture for the reading.
     */
    public CompletableFuture<Reading> enqueueReadingSpot(Spot spot, Unit unit) {
        return floorReaderService.addRequestToQueue(new FloorReaderService.ModbusRequest(spot, unit));
    }

    /**
     * Reads all floors without saving, returning a list of FloorReading records.
     *
     * @param unit The unit to read.
     * @return List of FloorReading.
     * @throws InterruptedException If interrupted during reading.
     */
    public List<FloorReading> readWithoutSaving(Unit unit) throws InterruptedException {
        List<FloorReading> allReadings = new ArrayList<>();
        List<Floor> floors = floorService.findAll();
        for (Floor floor : floors) {
            List<Reading> readingList = getFloorReadings(floor, unit);
            allReadings.add(new FloorReading(
                    floor.getFloorNumber(),
                    readingList,
                    readingList.stream().anyMatch(Reading::isSuccess),
                    (int) readingList.stream().mapToDouble(Reading::getReading).filter(reading -> reading > 0).sum()
            ));
        }
        return allReadings;
    }

    /**
     * Retrieves the readings for a specific floor using Modbus.
     * If the serial port is not initialized, returns an empty list.
     *
     * @param floor The Floor object from which to obtain the readings.
     * @param unit  The unit to read.
     * @return List of readings obtained for the floor.
     * @throws InterruptedException If the operation is interrupted.
     */
    private List<Reading> getFloorReadings(Floor floor, Unit unit) throws InterruptedException {
        if (!floorReaderService.isSerialInitialized()) {
            logger.warn("No serial ports found");
            return Collections.emptyList();
        }
        return floorReaderService.readFloor(floor, unit);
    }

    /**
     * Tries to read a floor, retrying on failure.
     *
     * @param floor       The floor to read.
     * @param allReadings The list to store the readings.
     * @param unit        The unit to read.
     * @return True if the reading was successful, false otherwise.
     * @throws InterruptedException If interrupted during reading.
     */
    private boolean tryReadingFloor(Floor floor, List<Reading> allReadings, Unit unit) throws InterruptedException {
        for (int i = 1; i <= NUMBER_OF_RETRIES; i++) {
            allReadings = getFloorReadings(floor, unit);
            if (allReadings.isEmpty()) {
                logger.info("No readings found on floor {}. Skipping to next floor.", floor.getFloorNumber());
                return true;
            }
            if (allReadings.stream().anyMatch(Reading::isSuccess)) {
                logger.info("Adding registers from floor {}", floor.getFloorNumber());
                readingService.saveAll(allReadings);
                return true;
            } else {
                logger.warn("All readings failed on floor {}. Retrying ({})", floor.getFloorNumber(), i);
            }
        }
        readingService.saveAll(allReadings);
        return false;
    }
}
