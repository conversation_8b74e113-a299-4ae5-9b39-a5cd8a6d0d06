package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Recipient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MailService {

    private static final Logger logger = LoggerFactory.getLogger(MailService.class);
    private final JavaMailSender mailSender;
    private final RecipientService recipientService;
    @Value("${spring.mail.username}")
    private String from;
    @Value("${mail.service.subject}")
    private String subject;
    @Value("${mail.service.body}")
    private String body;

    MailService(JavaMailSender mailSender, RecipientService recipientService) {
        this.mailSender = mailSender;
        this.recipientService = recipientService;
    }

    public void sendMail(String bodyText) {
        initializeAndSend(bodyText);
    }

    private void initializeAndSend(String body) {
        List<Recipient> recipientsList = recipientService.findAll();
        if (recipientsList.isEmpty()) {
            logger.warn("No recipients found. Not sending email.");
            return;
        }
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(from);
        String[] recipients = recipientsList.stream().map(Recipient::getEmail).toArray(String[]::new);
        message.setTo(recipients);
        message.setSubject(subject);
        message.setText(body);
        mailSender.send(message);
    }
}
