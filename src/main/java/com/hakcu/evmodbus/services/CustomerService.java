package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.config.CacheConfig;
import com.hakcu.evmodbus.entities.Customer;
import com.hakcu.evmodbus.repositories.CustomerRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class CustomerService {

    private final CustomerRepository customerRepository;

    CustomerService(CustomerRepository customerRepository) {
        this.customerRepository = customerRepository;
    }

    @Cacheable(CacheConfig.CUSTOMERS_CACHE)
    public List<Customer> findAll() {
        return customerRepository.findAllActive();
    }

    @Cacheable(value = CacheConfig.CUSTOMERS_CACHE, key = "#id")
    public Optional<Customer> findById(Long id) {
        return customerRepository.findActiveById(id);
    }

    @CacheEvict(value = CacheConfig.CUSTOMERS_CACHE, allEntries = true)
    public Customer save(Customer customer) {
        return customerRepository.save(customer);
    }

    @CacheEvict(value = CacheConfig.CUSTOMERS_CACHE, allEntries = true)
    public Customer delete(Long id) {
        Customer customer = customerRepository.findActiveById(id).orElseThrow(() -> new EntityNotFoundException("Customer not found"));
        customer.setDeletedAt(LocalDateTime.now());
        return customerRepository.save(customer);
    }

}
