package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.utils.SvgToPdfImageConverter;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.Locale;
import java.util.Map;

/**
 * Service for generating PDF reports of statistics.
 */
@Service
public class PdfReportService {

    // Constants for common strings
    private static final String CONSUMPTION_LABEL = "Consumo (kWh)";
    private static final String PERIOD_LABEL = "Período";
    private static final String HOUR_LABEL = "Hora";
    private static final String DAY_LABEL = "Día";
    private static final String MONTH_LABEL = "Mes";
    private static final String NO_DATA_MESSAGE = "No hay datos disponibles para este período.";
    private static final String NO_CHART_DATA_MESSAGE = "No hay datos disponibles para generar el gráfico.";
    private static final String CHART_ERROR_PREFIX = "Error al generar el gráfico: ";
    private static final String DATE_FORMAT_PATTERN = "dd/MM/yyyy";
    private static final String SHORT_DATE_FORMAT_PATTERN = "dd/MM";
    private static final String TIME_FORMAT_PATTERN = "HH:mm";
    private static final Locale SPANISH_LOCALE = Locale.of("es", "ES");

    // Constants for chart dimensions
    private static final float CHART_ASPECT_RATIO = 0.6f;
    private static final Font TITLE_FONT = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
    private static final Font SUBTITLE_FONT = new Font(Font.FontFamily.HELVETICA, 14, Font.BOLD);
    private static final Font NORMAL_FONT = new Font(Font.FontFamily.HELVETICA, 12, Font.NORMAL);
    private static final Font SMALL_FONT = new Font(Font.FontFamily.HELVETICA, 10, Font.NORMAL);
    private static final Font HEADER_FONT = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD);
    private final StatisticsService statisticsService;
    private final ChartService chartService;
    private final SvgToPdfImageConverter svgConverter;

    public PdfReportService(StatisticsService statisticsService, ChartService chartService, SvgToPdfImageConverter svgConverter) {
        this.statisticsService = statisticsService;
        this.chartService = chartService;
        this.svgConverter = svgConverter;
    }

    /**
     * Generates a daily report PDF for a specific spot and day.
     *
     * @param spotId       The ID of the spot.
     * @param day          The day for the report.
     * @param isCurrentDay Whether the selected day is the current day (to limit data).
     * @return ByteArrayOutputStream containing the PDF.
     * @throws RuntimeException If there's an error generating the report.
     */
    public ByteArrayOutputStream generateDailyReport(Long spotId, LocalDate day, boolean isCurrentDay) {
        try {
            // Initialize document
            DocumentSetup docSetup = initializeDocument();
            Document document = docSetup.document();

            // Get spot information
            Spot spot = statisticsService.getSpotById(spotId)
                    .orElseThrow(() -> new IllegalArgumentException("Spot not found"));

            // Add title
            String formattedDay = day.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN));
            String title = "Informe Diario: " + formattedDay;
            if (isCurrentDay) {
                title += " (hasta " + LocalTime.now().format(DateTimeFormatter.ofPattern(TIME_FORMAT_PATTERN)) + ")";
            }
            String subtitle = "Plaza " + spot.getSpotNumber() + " - Sótano " + spot.getFloor().getFloorNumber();

            addTitle(document, title);
            addSubtitle(document, subtitle);

            // Add summary section
            addDailySummarySection(document, spotId, day, isCurrentDay);

            // Add hourly consumption data
            addHourlyConsumptionSection(document, spotId, day, isCurrentDay);

            // Add daily chart
            addDailyChart(document, spotId, day, isCurrentDay);

            document.close();
            return docSetup.outputStream();
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Invalid parameters for daily report: " + e.getMessage(), e);
        } catch (DocumentException e) {
            throw new RuntimeException("Error creating PDF document for daily report", e);
        } catch (IOException e) {
            throw new RuntimeException("Error processing chart data for daily report", e);
        } catch (Exception e) {
            throw new RuntimeException("Unexpected error generating daily report", e);
        }
    }

    /**
     * Generates a monthly report PDF for a specific spot and month.
     *
     * @param spotId         The ID of the spot.
     * @param yearMonth      The year and month for the report.
     * @param isCurrentMonth Whether the selected month is the current month (to limit data).
     * @return ByteArrayOutputStream containing the PDF.
     * @throws RuntimeException If there's an error generating the report.
     */
    public ByteArrayOutputStream generateMonthlyReport(Long spotId, YearMonth yearMonth, boolean isCurrentMonth) {
        try {
            // Initialize document
            DocumentSetup docSetup = initializeDocument();
            Document document = docSetup.document();

            // Get spot information
            Spot spot = statisticsService.getSpotById(spotId)
                    .orElseThrow(() -> new IllegalArgumentException("Spot not found"));

            // Add title
            String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
            String title = "Informe Mensual: " + monthName + " " + yearMonth.getYear();
            if (isCurrentMonth) {
                title += " (hasta " + LocalDate.now().format(DateTimeFormatter.ofPattern(SHORT_DATE_FORMAT_PATTERN)) + ")";
            }
            String subtitle = "Plaza " + spot.getSpotNumber() + " - Sótano " + spot.getFloor().getFloorNumber();

            addTitle(document, title);
            addSubtitle(document, subtitle);

            // Add summary section
            addSummarySection(document, spotId, yearMonth, isCurrentMonth);

            // Add daily consumption data
            addDailyConsumptionSection(document, spotId, yearMonth, isCurrentMonth);

            // Add monthly chart
            addMonthlyChart(document, spotId, yearMonth, isCurrentMonth);

            document.close();
            return docSetup.outputStream();
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Invalid parameters for monthly report: " + e.getMessage(), e);
        } catch (DocumentException e) {
            throw new RuntimeException("Error creating PDF document for monthly report", e);
        } catch (IOException e) {
            throw new RuntimeException("Error processing chart data for monthly report", e);
        } catch (Exception e) {
            throw new RuntimeException("Unexpected error generating monthly report", e);
        }
    }

    /**
     * Generates a yearly report PDF for a specific spot and year.
     *
     * @param spotId        The ID of the spot.
     * @param year          The year for the report.
     * @param isCurrentYear Whether the selected year is the current year (to limit data).
     * @return ByteArrayOutputStream containing the PDF.
     * @throws RuntimeException If there's an error generating the report.
     */
    public ByteArrayOutputStream generateYearlyReport(Long spotId, int year, boolean isCurrentYear) {
        try {
            // Initialize document
            DocumentSetup docSetup = initializeDocument();
            Document document = docSetup.document();

            // Get spot information
            Spot spot = statisticsService.getSpotById(spotId)
                    .orElseThrow(() -> new IllegalArgumentException("Spot not found"));

            // Add title
            String title = "Informe Anual: " + year;
            if (isCurrentYear) {
                YearMonth currentMonth = YearMonth.now();
                title += " (hasta " + currentMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE) + ")";
            }
            String subtitle = "Plaza " + spot.getSpotNumber() + " - Sótano " + spot.getFloor().getFloorNumber();

            addTitle(document, title);
            addSubtitle(document, subtitle);

            // Add summary section
            addYearlySummarySection(document, spotId, year, isCurrentYear);

            // Add monthly consumption data
            addMonthlyConsumptionSection(document, spotId, year, isCurrentYear);

            // Add yearly chart
            addYearlyChart(document, spotId, year, isCurrentYear);

            document.close();
            return docSetup.outputStream();
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Invalid parameters for yearly report: " + e.getMessage(), e);
        } catch (DocumentException e) {
            throw new RuntimeException("Error creating PDF document for yearly report", e);
        } catch (IOException e) {
            throw new RuntimeException("Error processing chart data for yearly report", e);
        } catch (Exception e) {
            throw new RuntimeException("Unexpected error generating yearly report", e);
        }
    }

    /**
     * Initializes a new PDF document.
     *
     * @return A tuple containing the document and output stream.
     * @throws DocumentException If there's an error creating the document.
     */
    private DocumentSetup initializeDocument() throws DocumentException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, outputStream);
        document.open();
        return new DocumentSetup(document, outputStream);
    }

    /**
     * Calculates a date range for a day.
     *
     * @param day          The day for the range.
     * @param isCurrentDay Whether it's the current day (to limit end time).
     * @return A DateRange object with start and end times.
     */
    private DateRange calculateDayDateRange(LocalDate day, boolean isCurrentDay) {
        LocalDateTime dayStart = day.atStartOfDay();
        LocalDateTime dayEnd = isCurrentDay ? LocalDateTime.now() : day.atTime(23, 59, 59);
        return new DateRange(dayStart, dayEnd);
    }

    /**
     * Calculates a date range for a month.
     *
     * @param yearMonth      The year and month for the range.
     * @param isCurrentMonth Whether it's the current month (to limit end time).
     * @return A DateRange object with start and end times.
     */
    private DateRange calculateMonthDateRange(YearMonth yearMonth, boolean isCurrentMonth) {
        LocalDateTime monthStart = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime monthEnd = isCurrentMonth ? LocalDateTime.now() : yearMonth.atEndOfMonth().atTime(23, 59, 59);
        return new DateRange(monthStart, monthEnd);
    }

    /**
     * Calculates a date range for a year.
     *
     * @param year          The year for the range.
     * @param isCurrentYear Whether it's the current year (to limit end time).
     * @return A DateRange object with start and end times.
     */
    private DateRange calculateYearDateRange(int year, boolean isCurrentYear) {
        LocalDateTime yearStart = LocalDateTime.of(year, 1, 1, 0, 0);
        LocalDateTime yearEnd = isCurrentYear ? LocalDateTime.now() : LocalDateTime.of(year, 12, 31, 23, 59, 59);
        return new DateRange(yearStart, yearEnd);
    }

    /**
     * Creates a consumption table with data.
     *
     * @param data        The data to display in the table.
     * @param labelHeader The header for the label column.
     * @return A configured PdfPTable.
     */
    private PdfPTable createConsumptionTable(Map<String, Float> data, String labelHeader) {
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(100);

        // Add headers
        table.addCell(createHeaderCell(labelHeader));
        table.addCell(createHeaderCell(CONSUMPTION_LABEL));

        // Add data rows
        for (Map.Entry<String, Float> entry : data.entrySet()) {
            table.addCell(createCell(entry.getKey()));
            table.addCell(createCell(String.format("%.2f", entry.getValue())));
        }

        return table;
    }

    /**
     * Adds a section title to the document.
     *
     * @param document The PDF document.
     * @param title    The section title.
     * @throws DocumentException If there's an error adding the title.
     */
    private void addSectionTitle(Document document, String title) throws DocumentException {
        Paragraph sectionTitle = new Paragraph(title, SUBTITLE_FONT);
        sectionTitle.setSpacingBefore(15);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);
    }

    /**
     * Adds a chart to the PDF document.
     *
     * @param document    The PDF document.
     * @param data        The data for the chart.
     * @param chartTitle  The title of the chart.
     * @param yAxisLabel  The label for the Y axis.
     * @param description The description text for the chart.
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addChartToPdf(Document document, Map<String, Float> data, String chartTitle,
                               String yAxisLabel, String description) throws DocumentException {
        if (data.isEmpty()) {
            document.add(new Paragraph(NO_CHART_DATA_MESSAGE, NORMAL_FONT));
            return;
        }

        try {
            // Set chart dimensions
            float pageWidth = document.getPageSize().getWidth() - document.leftMargin() - document.rightMargin();
            float pageHeight = pageWidth * CHART_ASPECT_RATIO;

            // Generate chart SVG
            String barChartSvg = chartService.generateBarChartSvg(data, (int) pageWidth, (int) pageHeight, chartTitle, yAxisLabel);

            // Add description
            Paragraph chartDescription = new Paragraph(description, NORMAL_FONT);
            chartDescription.setAlignment(Element.ALIGN_CENTER);
            document.add(chartDescription);

            // Add chart to PDF
            svgConverter.addSvgToPdf(document, barChartSvg, pageWidth, pageHeight);
        } catch (IOException e) {
            document.add(new Paragraph(CHART_ERROR_PREFIX + e.getMessage(), SMALL_FONT));
        }
    }

    /**
     * Handles the case when no data is available for a section.
     *
     * @param document The PDF document.
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addNoDataMessage(Document document) throws DocumentException {
        document.add(new Paragraph(NO_DATA_MESSAGE, NORMAL_FONT));
    }

    /**
     * Adds a title to the document.
     */
    private void addTitle(Document document, String title) throws DocumentException {
        Paragraph titleParagraph = new Paragraph(title, TITLE_FONT);
        titleParagraph.setAlignment(Element.ALIGN_CENTER);
        titleParagraph.setSpacingAfter(10);
        document.add(titleParagraph);
    }

    /**
     * Adds a subtitle to the document.
     */
    private void addSubtitle(Document document, String subtitle) throws DocumentException {
        Paragraph subtitleParagraph = new Paragraph(subtitle, SUBTITLE_FONT);
        subtitleParagraph.setAlignment(Element.ALIGN_CENTER);
        subtitleParagraph.setSpacingAfter(20);
        document.add(subtitleParagraph);
    }

    /**
     * Adds the summary section for daily report.
     *
     * @param document     The PDF document.
     * @param spotId       The ID of the spot.
     * @param day          The day for the report.
     * @param isCurrentDay Whether it's the current day (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addDailySummarySection(Document document, Long spotId, LocalDate day, boolean isCurrentDay) throws DocumentException {
        addSectionTitle(document, "Resumen de Consumo");

        // Calculate consumption for the day
        DateRange dayRange = calculateDayDateRange(day, isCurrentDay);
        Float dailyConsumption = statisticsService.getTotalConsumption(spotId, dayRange.start(), dayRange.end());

        // Calculate consumption for the month
        YearMonth yearMonth = YearMonth.from(day);
        boolean isCurrentMonth = isCurrentDay && day.getMonth() == LocalDate.now().getMonth() && day.getYear() == LocalDate.now().getYear();
        DateRange monthRange = calculateMonthDateRange(yearMonth, isCurrentMonth);
        Float monthlyConsumption = statisticsService.getTotalConsumption(spotId, monthRange.start(), monthRange.end());

        // Create and add the summary table
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(100);

        // Add headers
        table.addCell(createHeaderCell(PERIOD_LABEL));
        table.addCell(createHeaderCell(CONSUMPTION_LABEL));

        // Add data rows
        String formattedDay = day.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN));
        table.addCell(createCell(formattedDay));
        table.addCell(createCell(String.format("%.2f", dailyConsumption)));

        String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
        table.addCell(createCell(monthName + " " + yearMonth.getYear()));
        table.addCell(createCell(String.format("%.2f", monthlyConsumption)));

        document.add(table);
    }

    /**
     * Adds the hourly consumption section for daily report.
     *
     * @param document     The PDF document.
     * @param spotId       The ID of the spot.
     * @param day          The day for the report.
     * @param isCurrentDay Whether it's the current day (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addHourlyConsumptionSection(Document document, Long spotId, LocalDate day, boolean isCurrentDay) throws DocumentException {
        addSectionTitle(document, "Consumo por Hora");

        // Get hourly consumption data
        DateRange dayRange = calculateDayDateRange(day, isCurrentDay);
        Map<String, Float> hourlyData = statisticsService.getHourlyConsumption(spotId, dayRange.start(), dayRange.end());

        if (hourlyData.isEmpty()) {
            addNoDataMessage(document);
            return;
        }

        // Create and add table for hourly data
        PdfPTable table = createConsumptionTable(hourlyData, HOUR_LABEL);
        document.add(table);
    }

    /**
     * Adds a daily chart to the document.
     *
     * @param document     The PDF document.
     * @param spotId       The ID of the spot.
     * @param day          The day for the report.
     * @param isCurrentDay Whether it's the current day (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     * @throws IOException       If there's an error processing the chart image.
     */
    private void addDailyChart(Document document, Long spotId, LocalDate day, boolean isCurrentDay) throws DocumentException, IOException {
        addSectionTitle(document, "Gráfico de Consumo por Hora");

        // Get hourly consumption data
        DateRange dayRange = calculateDayDateRange(day, isCurrentDay);
        Map<String, Float> hourlyData = statisticsService.getHourlyConsumption(spotId, dayRange.start(), dayRange.end());

        if (hourlyData.isEmpty()) {
            document.add(new Paragraph(NO_CHART_DATA_MESSAGE, NORMAL_FONT));
            return;
        }

        // Generate chart title and description
        String formattedDay = day.format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN));
        String chartTitle = "Consumo por hora - " + formattedDay;
        if (isCurrentDay) {
            chartTitle += " (hasta " + LocalTime.now().format(DateTimeFormatter.ofPattern(TIME_FORMAT_PATTERN)) + ")";
        }
        String chartDescription = "Consumo por hora para el día " + formattedDay;

        // Add chart to PDF
        addChartToPdf(document, hourlyData, chartTitle, CONSUMPTION_LABEL, chartDescription);
    }

    /**
     * Adds the summary section for monthly report.
     *
     * @param document       The PDF document.
     * @param spotId         The ID of the spot.
     * @param yearMonth      The year and month for the report.
     * @param isCurrentMonth Whether it's the current month (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addSummarySection(Document document, Long spotId, YearMonth yearMonth, boolean isCurrentMonth) throws DocumentException {
        addSectionTitle(document, "Resumen de Consumo");

        // Calculate consumption for the month
        DateRange monthRange = calculateMonthDateRange(yearMonth, isCurrentMonth);
        Float monthlyConsumption = statisticsService.getTotalConsumption(spotId, monthRange.start(), monthRange.end());

        // Calculate consumption for the year
        boolean isCurrentYear = isCurrentMonth && yearMonth.getYear() == LocalDate.now().getYear();
        DateRange yearRange = calculateYearDateRange(yearMonth.getYear(), isCurrentYear);
        Float yearlyConsumption = statisticsService.getTotalConsumption(spotId, yearRange.start(), yearRange.end());

        // Create and add the summary table
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(100);

        // Add headers
        table.addCell(createHeaderCell(PERIOD_LABEL));
        table.addCell(createHeaderCell(CONSUMPTION_LABEL));

        // Add data rows
        String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
        table.addCell(createCell(monthName + " " + yearMonth.getYear()));
        table.addCell(createCell(String.format("%.2f", monthlyConsumption)));

        table.addCell(createCell("Año " + yearMonth.getYear() + " (hasta la fecha)"));
        table.addCell(createCell(String.format("%.2f", yearlyConsumption)));

        document.add(table);
    }

    /**
     * Adds the summary section for yearly report.
     *
     * @param document      The PDF document.
     * @param spotId        The ID of the spot.
     * @param year          The year for the report.
     * @param isCurrentYear Whether it's the current year (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addYearlySummarySection(Document document, Long spotId, int year, boolean isCurrentYear) throws DocumentException {
        addSectionTitle(document, "Resumen de Consumo Anual");

        // Calculate consumption for the year
        DateRange yearRange = calculateYearDateRange(year, isCurrentYear);
        Float yearlyConsumption = statisticsService.getTotalConsumption(spotId, yearRange.start(), yearRange.end());

        // Create and add the summary table
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(100);

        // Add headers
        table.addCell(createHeaderCell(PERIOD_LABEL));
        table.addCell(createHeaderCell(CONSUMPTION_LABEL));

        // Add data row
        table.addCell(createCell("Año " + year));
        table.addCell(createCell(String.format("%.2f", yearlyConsumption)));

        document.add(table);
    }

    /**
     * Adds the daily consumption section for monthly report.
     *
     * @param document       The PDF document.
     * @param spotId         The ID of the spot.
     * @param yearMonth      The year and month for the report.
     * @param isCurrentMonth Whether it's the current month (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addDailyConsumptionSection(Document document, Long spotId, YearMonth yearMonth, boolean isCurrentMonth) throws DocumentException {
        addSectionTitle(document, "Consumo Diario");

        // Get daily consumption data
        DateRange monthRange = calculateMonthDateRange(yearMonth, isCurrentMonth);
        Map<String, Float> dailyData = statisticsService.getDailyConsumption(spotId, monthRange.start(), monthRange.end());

        if (dailyData.isEmpty()) {
            addNoDataMessage(document);
            return;
        }

        // Create and add table for daily data
        PdfPTable table = createConsumptionTable(dailyData, DAY_LABEL);
        document.add(table);
    }

    /**
     * Adds the monthly consumption section for yearly report.
     *
     * @param document      The PDF document.
     * @param spotId        The ID of the spot.
     * @param year          The year for the report.
     * @param isCurrentYear Whether it's the current year (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     */
    private void addMonthlyConsumptionSection(Document document, Long spotId, int year, boolean isCurrentYear) throws DocumentException {
        addSectionTitle(document, "Consumo Mensual");

        // Get monthly consumption data
        DateRange yearRange = calculateYearDateRange(year, isCurrentYear);
        Map<String, Float> monthlyData = statisticsService.getMonthlyConsumption(spotId, yearRange.start(), yearRange.end());

        if (monthlyData.isEmpty()) {
            addNoDataMessage(document);
            return;
        }

        // Create and add table for monthly data
        PdfPTable table = createConsumptionTable(monthlyData, MONTH_LABEL);
        document.add(table);
    }

    /**
     * Adds a monthly chart to the document.
     *
     * @param document       The PDF document.
     * @param spotId         The ID of the spot.
     * @param yearMonth      The year and month for the report.
     * @param isCurrentMonth Whether it's the current month (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     * @throws IOException       If there's an error processing the chart image.
     */
    private void addMonthlyChart(Document document, Long spotId, YearMonth yearMonth, boolean isCurrentMonth) throws DocumentException, IOException {
        addSectionTitle(document, "Gráfico de Consumo Diario");

        // Get daily consumption data
        DateRange monthRange = calculateMonthDateRange(yearMonth, isCurrentMonth);
        Map<String, Float> dailyData = statisticsService.getDailyConsumption(spotId, monthRange.start(), monthRange.end());

        if (dailyData.isEmpty()) {
            document.add(new Paragraph(NO_CHART_DATA_MESSAGE, NORMAL_FONT));
            return;
        }

        // Generate chart title and description
        String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE);
        String chartTitle = "Consumo diario - " + monthName + " " + yearMonth.getYear();
        if (isCurrentMonth) {
            chartTitle += " (hasta " + LocalDate.now().format(DateTimeFormatter.ofPattern(SHORT_DATE_FORMAT_PATTERN)) + ")";
        }
        String chartDescription = "Consumo diario para " + monthName + " " + yearMonth.getYear();

        // Add chart to PDF
        addChartToPdf(document, dailyData, chartTitle, CONSUMPTION_LABEL, chartDescription);
    }

    /**
     * Adds a yearly chart to the document.
     *
     * @param document      The PDF document.
     * @param spotId        The ID of the spot.
     * @param year          The year for the report.
     * @param isCurrentYear Whether it's the current year (to limit data).
     * @throws DocumentException If there's an error adding elements to the document.
     * @throws IOException       If there's an error processing the chart image.
     */
    private void addYearlyChart(Document document, Long spotId, int year, boolean isCurrentYear) throws DocumentException, IOException {
        addSectionTitle(document, "Gráfico de Consumo Mensual");

        // Get monthly consumption data
        DateRange yearRange = calculateYearDateRange(year, isCurrentYear);
        Map<String, Float> monthlyData = statisticsService.getMonthlyConsumption(spotId, yearRange.start(), yearRange.end());

        if (monthlyData.isEmpty()) {
            document.add(new Paragraph(NO_CHART_DATA_MESSAGE, NORMAL_FONT));
            return;
        }

        // Generate chart title and description
        String chartTitle = "Consumo mensual - " + year;
        if (isCurrentYear) {
            YearMonth currentMonth = YearMonth.now();
            chartTitle += " (hasta " + currentMonth.getMonth().getDisplayName(TextStyle.FULL, SPANISH_LOCALE) + ")";
        }
        String chartDescription = "Consumo mensual para el año " + year;

        // Add chart to PDF
        addChartToPdf(document, monthlyData, chartTitle, CONSUMPTION_LABEL, chartDescription);
    }

    /**
     * Creates a header cell for tables.
     */
    private PdfPCell createHeaderCell(String text) {
        PdfPCell cell = new PdfPCell(new Phrase(text, HEADER_FONT));
        cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setPadding(5);
        return cell;
    }

    /**
     * Creates a regular cell for tables.
     */
    private PdfPCell createCell(String text) {
        PdfPCell cell = new PdfPCell(new Phrase(text, NORMAL_FONT));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setPadding(5);
        return cell;
    }

    /**
     * Record to represent a date range with start and end times.
     */
    private record DateRange(LocalDateTime start, LocalDateTime end) {
    }

    /**
     * Record to hold document and output stream together.
     */
    private record DocumentSetup(Document document, ByteArrayOutputStream outputStream) {
    }
}
