package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Billing;
import com.hakcu.evmodbus.entities.BillingItem;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfWriter;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;

/**
 * Service for generating professional PDF reports for billing.
 */
@Service
public class BillingPdfService {

    // Color scheme
    private static final BaseColor PRIMARY_COLOR = new BaseColor(13, 110, 253);   // #0d6efd - Bootstrap primary blue
    private static final BaseColor SECONDARY_COLOR = new BaseColor(108, 117, 125); // #6c757d - Bootstrap secondary
    private static final BaseColor SUCCESS_COLOR = new BaseColor(25, 135, 84);    // #198754 - Bootstrap success
    private static final BaseColor DANGER_COLOR = new BaseColor(220, 53, 69);     // #dc3545 - Bootstrap danger
    private static final BaseColor WARNING_COLOR = new BaseColor(255, 193, 7);    // #ffc107 - Bootstrap warning
    private static final BaseColor INFO_COLOR = new BaseColor(13, 202, 240);      // #0dcaf0 - Bootstrap info
    private static final BaseColor LIGHT_COLOR = new BaseColor(248, 249, 250);    // #f8f9fa - Bootstrap light
    private static final BaseColor DARK_COLOR = new BaseColor(33, 37, 41);        // #212529 - Bootstrap dark
    private static final BaseColor BORDER_COLOR = new BaseColor(222, 226, 230);   // #dee2e6 - Bootstrap border

    // Font definitions - optimized for compact layout
    private static final Font TITLE_FONT = new Font(Font.FontFamily.HELVETICA, 16, Font.BOLD, PRIMARY_COLOR);
    private static final Font SUBTITLE_FONT = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD, SECONDARY_COLOR);
    private static final Font HEADING_FONT = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD, DARK_COLOR);
    private static final Font NORMAL_FONT = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL, DARK_COLOR);
    private static final Font SMALL_FONT = new Font(Font.FontFamily.HELVETICA, 7, Font.NORMAL, SECONDARY_COLOR);
    private static final Font BOLD_FONT = new Font(Font.FontFamily.HELVETICA, 9, Font.BOLD, DARK_COLOR);
    private static final Font LABEL_FONT = new Font(Font.FontFamily.HELVETICA, 9, Font.BOLD, SECONDARY_COLOR);
    private static final Font HEADER_FONT = new Font(Font.FontFamily.HELVETICA, 9, Font.BOLD, BaseColor.WHITE);
    private static final Font FOOTER_FONT = new Font(Font.FontFamily.HELVETICA, 7, Font.NORMAL, SECONDARY_COLOR);
    private static final Font HIGHLIGHT_FONT = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD, PRIMARY_COLOR);
    private static final Font TOTAL_FONT = new Font(Font.FontFamily.HELVETICA, 11, Font.BOLD, PRIMARY_COLOR);

    // Company information - would typically come from application properties
    private static final String COMPANY_NAME = "Aparcamientos Sarrià Término";
    private static final String COMPANY_ADDRESS = "Pg. Reina Elisenda de Montcada 13, sótano 1, 08034 Barcelona";
    private static final String COMPANY_PHONE = "+34 91 123 45 67";
    private static final String COMPANY_EMAIL = "<EMAIL>";
    private static final String COMPANY_TAX_ID = "A08376733";

    // Document margins - reduced for compact layout
    private static final float MARGIN_LEFT = 28f;   // ~0.4 inch
    private static final float MARGIN_RIGHT = 28f;  // ~0.4 inch
    private static final float MARGIN_TOP = 50f;    // ~0.7 inch
    private static final float MARGIN_BOTTOM = 50f; // ~0.7 inch

    /**
     * Generates a PDF report for a billing.
     *
     * @param billing The billing to generate the report for
     * @return The PDF report as a byte array
     */
    public ByteArrayOutputStream generateBillingPdf(Billing billing) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            // Create document with margins
            Document document = new Document(PageSize.A4, MARGIN_LEFT, MARGIN_RIGHT, MARGIN_TOP, MARGIN_BOTTOM);

            // Create PDF writer and set up page events
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            writer.setPageEvent(new BillingDocumentPageEvent(billing));

            // Set document metadata
            document.addTitle("Factura #" + billing.getBillNumber());
            document.addSubject("Factura para " + billing.getCustomer().getFirstName() + " " + billing.getCustomer().getLastName());
            document.addKeywords("factura, evmodbus, electricidad, cargador");
            document.addCreator(COMPANY_NAME);
            document.addAuthor(COMPANY_NAME);

            // Open the document
            document.open();

            // Add billing and customer information in a two-column layout
            addBillingAndCustomerInfo(document, billing);

            // Add billing items
            addBillingItems(document, billing.getItems());

            // Add comments if they exist
            if (billing.getComments() != null && !billing.getComments().trim().isEmpty()) {
                addComments(document, billing.getComments());
            }

            // Add billing summary
            addBillingSummary(document, billing);

            // Payment information removed as per requirements

            document.close();
            return outputStream;
        } catch (Exception e) {
            throw new RuntimeException("Error generating billing PDF", e);
        }
    }

    /**
     * Adds billing and customer information in a two-column layout - compact version.
     */
    private void addBillingAndCustomerInfo(Document document, Billing billing) throws DocumentException {
        // Create a table with two columns
        PdfPTable mainTable = new PdfPTable(2);
        mainTable.setWidthPercentage(100);
        mainTable.setSpacingAfter(10); // Reduced spacing

        // Left column: Billing information
        PdfPCell billingCell = new PdfPCell();
        billingCell.setBorder(Rectangle.NO_BORDER);
        billingCell.setPaddingRight(5); // Reduced padding

        // Add billing info title
        Paragraph billingTitle = new Paragraph("Información de facturación", HEADING_FONT);
        billingTitle.setSpacingAfter(5); // Reduced spacing
        billingCell.addElement(billingTitle);

        // Create a nested table for billing details
        PdfPTable billingTable = new PdfPTable(2);
        billingTable.setWidthPercentage(100);
        billingTable.setWidths(new float[]{1, 2});

        // Add a subtle background to the billing info section
        PdfPCell billingInfoCell = new PdfPCell();
        billingInfoCell.setColspan(2);
        billingInfoCell.setPadding(5); // Reduced padding
        billingInfoCell.setBackgroundColor(LIGHT_COLOR);

        // Create a nested table for the billing details
        PdfPTable billingDetailsTable = new PdfPTable(2);
        billingDetailsTable.setWidthPercentage(100);

        // Billing period
        addStyledTableCell(billingDetailsTable, "Período:", LABEL_FONT, Element.ALIGN_LEFT); // Shortened label
        addStyledTableCell(billingDetailsTable, formatYearMonth(billing.getBillingPeriod()), NORMAL_FONT, Element.ALIGN_RIGHT);

        // Issue date
        addStyledTableCell(billingDetailsTable, "Emisión:", LABEL_FONT, Element.ALIGN_LEFT); // Shortened label
        addStyledTableCell(billingDetailsTable, formatDate(billing.getIssueDate()), NORMAL_FONT, Element.ALIGN_RIGHT);

        // Due date
        addStyledTableCell(billingDetailsTable, "Vencimiento:", LABEL_FONT, Element.ALIGN_LEFT); // Shortened label
        addStyledTableCell(billingDetailsTable, formatDate(billing.getDueDate()), NORMAL_FONT, Element.ALIGN_RIGHT);

        // Payment status and payment date removed as per requirements

        billingInfoCell.addElement(billingDetailsTable);
        billingTable.addCell(billingInfoCell);
        billingCell.addElement(billingTable);
        mainTable.addCell(billingCell);

        // Right column: Customer information
        PdfPCell customerCell = new PdfPCell();
        customerCell.setBorder(Rectangle.NO_BORDER);
        customerCell.setPaddingLeft(5); // Reduced padding

        // Add customer info title
        Paragraph customerTitle = new Paragraph("Información del cliente", HEADING_FONT);
        customerTitle.setSpacingAfter(5); // Reduced spacing
        customerCell.addElement(customerTitle);

        // Create a nested table for customer details
        PdfPTable customerTable = new PdfPTable(1);
        customerTable.setWidthPercentage(100);

        // Add a subtle background to the customer info section
        PdfPCell customerInfoCell = new PdfPCell();
        customerInfoCell.setPadding(5); // Reduced padding
        customerInfoCell.setBackgroundColor(LIGHT_COLOR);

        // Create a nested table for the customer details
        PdfPTable customerDetailsTable = new PdfPTable(2);
        customerDetailsTable.setWidthPercentage(100);

        // Customer name
        addStyledTableCell(customerDetailsTable, "Nombre:", LABEL_FONT, Element.ALIGN_LEFT);
        addStyledTableCell(customerDetailsTable, billing.getCustomer().getFirstName() + " " + billing.getCustomer().getLastName(),
                BOLD_FONT, Element.ALIGN_RIGHT);

        // Customer DNI
        if (billing.getCustomer().getDni() != null && !billing.getCustomer().getDni().isEmpty()) {
            addStyledTableCell(customerDetailsTable, "DNI:", LABEL_FONT, Element.ALIGN_LEFT);
            addStyledTableCell(customerDetailsTable, billing.getCustomer().getDni(), NORMAL_FONT, Element.ALIGN_RIGHT);
        }

        // Customer address - only include if there's space
        if (billing.getCustomer().getAddress() != null && !billing.getCustomer().getAddress().isEmpty()) {
            addStyledTableCell(customerDetailsTable, "Dirección:", LABEL_FONT, Element.ALIGN_LEFT); // Shortened label
            addStyledTableCell(customerDetailsTable, billing.getCustomer().getFullAddress(), NORMAL_FONT, Element.ALIGN_RIGHT);
        }

        // Customer phone - prioritize over email to save space
        if (billing.getCustomer().getMainPhoneNumber() != null && !billing.getCustomer().getMainPhoneNumber().isEmpty()) {
            addStyledTableCell(customerDetailsTable, "Teléfono:", LABEL_FONT, Element.ALIGN_LEFT); // Shortened label
            addStyledTableCell(customerDetailsTable, billing.getCustomer().getMainPhoneNumber(), NORMAL_FONT, Element.ALIGN_RIGHT);
        }

        // Customer email - only include if there's space
        if (billing.getCustomer().getMainEmailAddress() != null && !billing.getCustomer().getMainEmailAddress().isEmpty()) {
            addStyledTableCell(customerDetailsTable, "Email:", LABEL_FONT, Element.ALIGN_LEFT);
            addStyledTableCell(customerDetailsTable, billing.getCustomer().getMainEmailAddress(), NORMAL_FONT, Element.ALIGN_RIGHT);
        }

        customerInfoCell.addElement(customerDetailsTable);
        customerTable.addCell(customerInfoCell);
        customerCell.addElement(customerTable);
        mainTable.addCell(customerCell);

        document.add(mainTable);
    }

    /**
     * Helper method to add a styled cell to a table.
     */
    private void addStyledTableCell(PdfPTable table, String text, Font font, int alignment) {
        PdfPCell cell = new PdfPCell(new Phrase(text, font));
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setHorizontalAlignment(alignment);
        cell.setPadding(5);
        table.addCell(cell);
    }

    /**
     * Adds the billing items table to the document - compact version.
     */
    private void addBillingItems(Document document, List<BillingItem> items) throws DocumentException {
        // Add section title
        Paragraph subtitle = new Paragraph("Detalle de consumo", SUBTITLE_FONT);
        subtitle.setSpacingAfter(5); // Reduced spacing
        document.add(subtitle);

        // Create table with 4 columns
        PdfPTable table = new PdfPTable(4);
        table.setWidthPercentage(100);
        table.setSpacingAfter(10); // Reduced spacing

        // Set column widths
        table.setWidths(new float[]{2.5f, 1f, 1f, 1f});

        // Table header - more compact
        addStyledHeaderCell(table, "Plaza", Element.ALIGN_LEFT);
        addStyledHeaderCell(table, "Consumo", Element.ALIGN_RIGHT); // Shortened header
        addStyledHeaderCell(table, "Tarifa", Element.ALIGN_RIGHT); // Shortened header
        addStyledHeaderCell(table, "Importe", Element.ALIGN_RIGHT); // Shortened header

        // Table data with alternating row colors - more compact
        boolean alternate = false;
        for (BillingItem item : items) {
            // Spot information - more compact
            PdfPCell spotCell = new PdfPCell(new Phrase(
                    "Plaza " + item.getSpot().getSpotNumber() + " - Sótano " + item.getSpot().getFloor().getFloorNumber(), // Shortened format
                    NORMAL_FONT));
            spotCell.setPadding(4); // Reduced padding
            spotCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            spotCell.setBackgroundColor(alternate ? LIGHT_COLOR : BaseColor.WHITE);
            table.addCell(spotCell);

            // Consumption
            PdfPCell consumptionCell = new PdfPCell(new Phrase(formatFloat(item.getConsumption()), NORMAL_FONT));
            consumptionCell.setPadding(4); // Reduced padding
            consumptionCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            consumptionCell.setBackgroundColor(alternate ? LIGHT_COLOR : BaseColor.WHITE);
            table.addCell(consumptionCell);

            // Rate
            PdfPCell rateCell = new PdfPCell(new Phrase(formatCurrency(item.getRateApplied()), NORMAL_FONT));
            rateCell.setPadding(4); // Reduced padding
            rateCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            rateCell.setBackgroundColor(alternate ? LIGHT_COLOR : BaseColor.WHITE);
            table.addCell(rateCell);

            // Amount
            PdfPCell amountCell = new PdfPCell(new Phrase(formatCurrency(item.getAmount()), BOLD_FONT));
            amountCell.setPadding(4); // Reduced padding
            amountCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            amountCell.setBackgroundColor(alternate ? LIGHT_COLOR : BaseColor.WHITE);
            table.addCell(amountCell);

            // Toggle alternating row color
            alternate = !alternate;
        }

        document.add(table);
    }

    /**
     * Helper method to add a styled header cell to a table.
     */
    private void addStyledHeaderCell(PdfPTable table, String text, int alignment) {
        PdfPCell cell = new PdfPCell(new Phrase(text, HEADER_FONT));
        cell.setBackgroundColor(PRIMARY_COLOR);
        cell.setHorizontalAlignment(alignment);
        cell.setPadding(8);
        cell.setBorderColor(PRIMARY_COLOR);
        table.addCell(cell);
    }

    /**
     * Adds comments section to the document if comments exist - compact version.
     */
    private void addComments(Document document, String comments) throws DocumentException {
        // Add section title inline with comments to save space
        PdfPTable table = new PdfPTable(1);
        table.setWidthPercentage(100);
        table.setSpacingAfter(10); // Reduced spacing

        // Add comments in a styled cell with title
        PdfPCell cell = new PdfPCell();
        cell.setPadding(5); // Reduced padding
        cell.setBorderColor(BORDER_COLOR);
        cell.setBorderWidth(1);
        cell.setBackgroundColor(LIGHT_COLOR);

        // Add title and comments in the same cell
        Paragraph commentsParagraph = new Paragraph();
        commentsParagraph.add(new Chunk("Comentarios: ", HEADING_FONT));
        commentsParagraph.add(new Chunk(comments, NORMAL_FONT));
        cell.addElement(commentsParagraph);

        table.addCell(cell);
        document.add(table);
    }

    /**
     * Adds the billing summary section to the document - compact version.
     */
    private void addBillingSummary(Document document, Billing billing) throws DocumentException {
        // Create a container for the summary table (right-aligned)
        PdfPTable container = new PdfPTable(1);
        container.setWidthPercentage(100);
        container.setSpacingAfter(10);
        container.setHorizontalAlignment(Element.ALIGN_RIGHT);

        // Create a cell to hold the summary table
        PdfPCell containerCell = new PdfPCell();
        containerCell.setBorder(Rectangle.NO_BORDER);
        containerCell.setHorizontalAlignment(Element.ALIGN_RIGHT);

        // Create the summary table - right-aligned
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(50);
        table.setHorizontalAlignment(Element.ALIGN_RIGHT);
        table.setWidths(new float[]{1.5f, 1f});

        // Add a subtle background and border to the summary table
        PdfPCell summaryCell = new PdfPCell();
        summaryCell.setColspan(2);
        summaryCell.setPadding(5); // Reduced padding
        summaryCell.setBackgroundColor(LIGHT_COLOR);
        summaryCell.setBorderColor(BORDER_COLOR);
        summaryCell.setBorderWidth(1);

        // Create a nested table for the summary details
        PdfPTable summaryDetailsTable = new PdfPTable(2);
        summaryDetailsTable.setWidthPercentage(100);

        // Add title inside the summary table
        PdfPCell titleCell = new PdfPCell(new Phrase("Resumen de facturación", HEADING_FONT));
        titleCell.setColspan(2);
        titleCell.setBorder(Rectangle.NO_BORDER);
        titleCell.setBorderWidthBottom(1);
        titleCell.setBorderColorBottom(BORDER_COLOR);
        titleCell.setPadding(3);
        titleCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        summaryDetailsTable.addCell(titleCell);

        // Fixed fee
        addCompactSummaryRow(summaryDetailsTable, "Cuota fija:", billing.getFixedFeeAmount(), false, false);

        // Consumption amount
        addCompactSummaryRow(summaryDetailsTable, "Consumo:", billing.getConsumptionAmount(), false, false);

        // Initial subtotal (before discount)
        BigDecimal initialSubtotal = billing.getFixedFeeAmount().add(billing.getConsumptionAmount());
        addCompactSummaryRow(summaryDetailsTable, "Subtotal:", initialSubtotal, false, true);

        // Discount (if applicable)
        BigDecimal subtotalAfterDiscount;
        if (billing.getDiscountAmount() != null && billing.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            // Add discount row with negative sign - using a custom method for the discount with red color
            addDiscountRow(summaryDetailsTable, "Descuento:", billing.getDiscountAmount());

            // Calculate subtotal after discount
            subtotalAfterDiscount = initialSubtotal.subtract(billing.getDiscountAmount());

            // Add subtotal after discount row
            addCompactSummaryRow(summaryDetailsTable, "Subtotal con dto.:", subtotalAfterDiscount, false, true); // Shortened label
        }

        // Add a separator line before tax
        PdfPCell separatorCell = new PdfPCell(new Phrase(""));
        separatorCell.setColspan(2);
        separatorCell.setPadding(0);
        separatorCell.setFixedHeight(1);
        separatorCell.setBorder(Rectangle.BOTTOM);
        separatorCell.setBorderColorBottom(BORDER_COLOR);
        summaryDetailsTable.addCell(separatorCell);

        // Tax amount
        addCompactSummaryRow(summaryDetailsTable, "IVA (21%):", billing.getTaxAmount(), false, false);

        // Total amount
        addCompactSummaryRow(summaryDetailsTable, "TOTAL:", billing.getTotalAmount(), true, false);

        summaryCell.addElement(summaryDetailsTable);
        table.addCell(summaryCell);
        containerCell.addElement(table);
        container.addCell(containerCell);

        document.add(container);
    }

    /**
     * Helper method to add a row to the summary table - compact version.
     */
    private void addCompactSummaryRow(PdfPTable table, String label, BigDecimal value, boolean isTotal, boolean addBottomBorder) {
        // Label cell
        PdfPCell labelCell = new PdfPCell();
        labelCell.setBorder(Rectangle.NO_BORDER);
        if (addBottomBorder) {
            labelCell.setBorder(Rectangle.BOTTOM);
            labelCell.setBorderColorBottom(BORDER_COLOR);
        }
        labelCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        labelCell.setPadding(3); // Reduced padding

        // Use different font for total row
        Font labelFont = isTotal ? TOTAL_FONT : LABEL_FONT;
        Paragraph labelParagraph = new Paragraph(label, labelFont);
        labelParagraph.setAlignment(Element.ALIGN_LEFT);
        labelCell.addElement(labelParagraph);
        table.addCell(labelCell);

        // Value cell
        PdfPCell valueCell = new PdfPCell();
        valueCell.setBorder(Rectangle.NO_BORDER);
        if (addBottomBorder) {
            valueCell.setBorder(Rectangle.BOTTOM);
            valueCell.setBorderColorBottom(BORDER_COLOR);
        }
        valueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        valueCell.setPadding(3); // Reduced padding

        // Use different font for total row
        Font valueFont = isTotal ? TOTAL_FONT : NORMAL_FONT;
        Paragraph valueParagraph = new Paragraph(formatCurrency(value), valueFont);
        valueParagraph.setAlignment(Element.ALIGN_RIGHT);
        valueCell.addElement(valueParagraph);
        table.addCell(valueCell);
    }

    /**
     * Formats a YearMonth object to a localized string (e.g., "enero 2023").
     */
    private String formatYearMonth(java.time.YearMonth yearMonth) {
        return yearMonth.format(DateTimeFormatter.ofPattern("MMM yyyy", Locale.of("es", "ES"))); // Shortened month format
    }

    /**
     * Formats a LocalDate object to a string (e.g., "01/01/2023").
     */
    private String formatDate(java.time.LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
    }

    /**
     * Formats a float value as a consumption amount with kWh unit.
     */
    private String formatFloat(Float value) {
        return String.format("%.1f kWh", value); // Reduced to 1 decimal place to save space
    }

    /**
     * Formats a BigDecimal as a currency amount with euro symbol.
     */
    private String formatCurrency(BigDecimal value) {
        return String.format("%.2f €", value);
    }

    /**
     * Formats a BigDecimal as a negative currency amount with euro symbol.
     */
    private String formatNegativeCurrency(BigDecimal value) {
        return String.format("-%.2f €", value);
    }

    /**
     * Helper method to add a discount row to the summary table with proper right alignment.
     */
    private void addDiscountRow(PdfPTable table, String label, BigDecimal value) {
        // Label cell
        PdfPCell labelCell = new PdfPCell();
        labelCell.setBorder(Rectangle.NO_BORDER);
        labelCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        labelCell.setPadding(3); // Reduced padding

        Paragraph labelParagraph = new Paragraph(label, LABEL_FONT);
        labelParagraph.setAlignment(Element.ALIGN_LEFT);
        labelCell.addElement(labelParagraph);
        table.addCell(labelCell);

        // Value cell with red color for discount
        PdfPCell valueCell = new PdfPCell();
        valueCell.setBorder(Rectangle.NO_BORDER);
        valueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        valueCell.setPadding(3); // Reduced padding

        // Use red color for discount amount
        Font discountFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL, DANGER_COLOR);
        Paragraph valueParagraph = new Paragraph(formatNegativeCurrency(value), discountFont);
        valueParagraph.setAlignment(Element.ALIGN_RIGHT);
        valueCell.addElement(valueParagraph);
        table.addCell(valueCell);
    }

    /**
     * Page event handler for adding headers and footers to each page.
     */
    private class BillingDocumentPageEvent extends PdfPageEventHelper {
        private final Billing billing;

        public BillingDocumentPageEvent(Billing billing) {
            this.billing = billing;
        }

        @Override
        public void onStartPage(PdfWriter writer, Document document) {
            try {
                // Simplified header with only company name and bill number
                PdfPTable headerTable = new PdfPTable(2);
                headerTable.setWidthPercentage(100);
                headerTable.setWidths(new float[]{1, 1});
                headerTable.setSpacingAfter(10);

                // Company name (left column)
                PdfPCell companyNameCell = new PdfPCell();
                companyNameCell.setBorder(Rectangle.NO_BORDER);
                companyNameCell.setPadding(5);

                // Only show company name
                Paragraph companyNameParagraph = new Paragraph(COMPANY_NAME, TITLE_FONT);
                companyNameCell.addElement(companyNameParagraph);
                headerTable.addCell(companyNameCell);

                // Bill number (right column)
                PdfPCell billNumberCell = new PdfPCell();
                billNumberCell.setBorder(Rectangle.NO_BORDER);
                billNumberCell.setPadding(5);
                billNumberCell.setHorizontalAlignment(Element.ALIGN_RIGHT);

                // Show bill number if available
                if (billing != null && billing.getBillNumber() != null) {
                    Paragraph billNumberParagraph = new Paragraph();
                    billNumberParagraph.setAlignment(Element.ALIGN_RIGHT);
                    billNumberParagraph.add(new Chunk("Factura Nº: ", SUBTITLE_FONT));
                    billNumberParagraph.add(new Chunk(billing.getBillNumber().toString(), HIGHLIGHT_FONT));
                    billNumberCell.addElement(billNumberParagraph);
                }
                headerTable.addCell(billNumberCell);

                // Add the header table to the document
                document.add(headerTable);

                // Add a separator line
                PdfPTable separatorTable = new PdfPTable(1);
                separatorTable.setWidthPercentage(100);
                PdfPCell separatorCell = new PdfPCell();
                separatorCell.setBorderWidthBottom(1);
                separatorCell.setBorderColorBottom(PRIMARY_COLOR);
                separatorCell.setBorderWidthTop(0);
                separatorCell.setBorderWidthLeft(0);
                separatorCell.setBorderWidthRight(0);
                separatorCell.setFixedHeight(0);
                separatorTable.addCell(separatorCell);
                document.add(separatorTable);
            } catch (DocumentException e) {
                throw new RuntimeException("Error adding header", e);
            }
        }

        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            try {
                // Enhanced footer with all company details
                PdfPTable footerTable = new PdfPTable(2);
                footerTable.setWidthPercentage(100);
                footerTable.setWidths(new float[]{1, 1});

                // Page number (left column)
                PdfPCell pageNumberCell = new PdfPCell(new Phrase("Página " + writer.getPageNumber(), FOOTER_FONT));
                pageNumberCell.setBorder(Rectangle.TOP);
                pageNumberCell.setBorderColorTop(BORDER_COLOR);
                pageNumberCell.setPaddingTop(3);
                footerTable.addCell(pageNumberCell);

                // Company details (right column)
                PdfPCell companyCell = new PdfPCell();
                companyCell.setBorder(Rectangle.TOP);
                companyCell.setBorderColorTop(BORDER_COLOR);
                companyCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                companyCell.setPaddingTop(3);
                companyCell.setNoWrap(true); // Prevent line wrapping

                // Create a paragraph with company details in two lines
                Paragraph companyDetails = new Paragraph();
                companyDetails.setAlignment(Element.ALIGN_RIGHT);

                // First line: Company name and address
                Phrase companyFirstLine = new Phrase();
                companyFirstLine.add(new Chunk(COMPANY_NAME + " | " + COMPANY_ADDRESS, FOOTER_FONT));
                companyDetails.add(companyFirstLine);

                // Add a line break
                companyDetails.add(Chunk.NEWLINE);

                // Second line: Phone, Email, and Tax ID
                Phrase companySecondLine = new Phrase();
                companySecondLine.add(new Chunk("Tel: " + COMPANY_PHONE + " | Email: " + COMPANY_EMAIL + " | CIF: " + COMPANY_TAX_ID, FOOTER_FONT));
                companyDetails.add(companySecondLine);

                companyCell.addElement(companyDetails);
                footerTable.addCell(companyCell);

                // Position the footer at the bottom of the page
                footerTable.setTotalWidth(document.right() - document.left());
                footerTable.writeSelectedRows(0, -1, document.left(), document.bottom() + 15, writer.getDirectContent());
            } catch (Exception e) {
                throw new RuntimeException("Error adding footer", e);
            }
        }
    }
}
