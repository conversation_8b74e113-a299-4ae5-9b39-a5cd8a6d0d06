package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.config.CacheConfig;
import com.hakcu.evmodbus.entities.*;
import com.hakcu.evmodbus.enums.BillingStatus;
import com.hakcu.evmodbus.repositories.BillingRepository;
import com.hakcu.evmodbus.services.consumption.ConsumptionCalculationService;
import jakarta.persistence.EntityNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing billing operations.
 */
@Service
@Transactional
public class BillingService {
    private static final Logger logger = LoggerFactory.getLogger(BillingService.class);
    // Tax rate (e.g., 21% VAT)
    private static final BigDecimal TAX_RATE = new BigDecimal("0.21");
    private final BillingRepository billingRepository;
    private final CustomerService customerService;
    private final BillingRateService billingRateService;
    private final ConsumptionCalculationService consumptionCalculationService;

    public BillingService(BillingRepository billingRepository,
                          CustomerService customerService,
                          BillingRateService billingRateService,
                          ConsumptionCalculationService consumptionCalculationService) {
        this.billingRepository = billingRepository;
        this.customerService = customerService;
        this.billingRateService = billingRateService;
        this.consumptionCalculationService = consumptionCalculationService;
    }

    @Cacheable(CacheConfig.BILLINGS_CACHE)
    public List<Billing> findAll() {
        return billingRepository.findAllActive();
    }

    @Cacheable(value = CacheConfig.BILLINGS_CACHE, key = "#id")
    public Optional<Billing> findById(Long id) {
        return billingRepository.findActiveById(id);
    }

    @Cacheable(value = CacheConfig.BILLINGS_CACHE, key = "'billNumber_' + #billNumber")
    public Optional<Billing> findByBillNumber(Long billNumber) {
        return billingRepository.findByBillNumber(billNumber);
    }

    /**
     * Gets the next available bill number.
     *
     * @return The next available bill number
     */
    public Long getNextBillNumber() {
        return billingRepository.findMaxBillNumber().map(max -> max + 1).orElse(1L);
    }

    /**
     * Validates if a bill number is available and greater than the current maximum.
     *
     * @param billNumber The bill number to validate
     * @return true if the bill number is valid, false otherwise
     */
    public boolean isValidBillNumber(Long billNumber) {
        if (billNumber == null || billNumber < 1) {
            return false;
        }

        // Check if the bill number already exists
        if (billingRepository.existsByBillNumber(billNumber)) {
            return false;
        }

        // Check if the bill number is greater than the current maximum
        Long maxBillNumber = billingRepository.findMaxBillNumber().orElse(0L);
        return billNumber > maxBillNumber;
    }

    @Cacheable(value = CacheConfig.BILLINGS_CACHE, key = "'customer_' + #customerId")
    public List<Billing> findAllByCustomerId(Long customerId) {
        return billingRepository.findAllActiveByCustomerId(customerId);
    }

    @Cacheable(value = CacheConfig.FILTERED_BILLINGS_CACHE,
            key = "#customerId + '_' + (#period != null ? #period.toString() : 'null') + '_' + (#paid != null ? #paid.toString() : 'null') + '_' + (#billNumber != null ? #billNumber.toString() : 'null') + '_' + #page + '_' + #size")
    public Page<Billing> findPaginatedWithFilters(Long customerId, YearMonth period, Boolean paid, Long billNumber, int page, int size) {
        // Create pageable (sorting is handled in the repository query)
        Pageable pageable = PageRequest.of(page, size);
        return billingRepository.findAllActiveWithFilters(customerId, period, paid, billNumber, pageable);
    }

    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.BILLING_ITEMS_CACHE,
            CacheConfig.PAGINATED_BILLINGS_CACHE, CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public Billing save(Billing billing) {
        return billingRepository.save(billing);
    }

    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.BILLING_ITEMS_CACHE,
            CacheConfig.PAGINATED_BILLINGS_CACHE, CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public void delete(Long id) {
        Billing billing = billingRepository.findActiveById(id)
                .orElseThrow(() -> new EntityNotFoundException("Billing not found"));
        billing.setDeletedAt(LocalDateTime.now());
        billingRepository.save(billing);
    }

    /**
     * Updates the status of a billing.
     *
     * @param id     The ID of the billing to update
     * @param status The new status to set
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.PAGINATED_BILLINGS_CACHE,
            CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public void updateBillingStatus(Long id, BillingStatus status) {
        try {
            Billing billing = billingRepository.findActiveById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Billing not found"));
            billing.setStatus(status);

            // If status is PAID, set the paid date
            if (status == BillingStatus.PAID) {
                billing.setPaidDate(LocalDate.now());
            } else {
                billing.setPaidDate(null);
            }

            billingRepository.save(billing);
        } catch (Exception e) {
            logger.error("Error updating billing status for billing {}: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Marks a billing as paid with the current date.
     *
     * @param id The ID of the billing to mark as paid
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.PAGINATED_BILLINGS_CACHE,
            CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public void markAsPaid(Long id) {
        updateBillingStatus(id, BillingStatus.PAID);
    }

    /**
     * Marks a billing as unpaid.
     *
     * @param id The ID of the billing to mark as unpaid
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.PAGINATED_BILLINGS_CACHE,
            CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public void markAsUnpaid(Long id) {
        updateBillingStatus(id, BillingStatus.PENDING);
    }

    /**
     * Updates a draft billing with new details.
     * Only billings in DRAFT status can be updated.
     *
     * @param id             The ID of the billing to update
     * @param billNumber     The new bill number
     * @param billingRateId  The ID of the new billing rate
     * @param discountAmount The discount amount to apply
     * @param comments       The comments for the billing
     * @throws IllegalStateException   if the billing is not in DRAFT status
     * @throws EntityNotFoundException if the billing or billing rate is not found
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.PAGINATED_BILLINGS_CACHE,
            CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public void updateDraftBilling(Long id, Long billNumber, Long billingRateId, BigDecimal discountAmount, String comments) {
        try {
            // Step 1: Find and validate the billing
            Billing billing = billingRepository.findActiveById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Billing not found"));

            if (billing.getStatus() != BillingStatus.DRAFT) {
                throw new IllegalStateException("Solo se pueden editar facturas en estado borrador");
            }

            // Step 2: Validate bill number uniqueness
            if (!billing.getBillNumber().equals(billNumber)) {
                // Check if the new bill number is already in use by another non-deleted billing
                Optional<Billing> existingBilling = billingRepository.findByBillNumber(billNumber);
                if (existingBilling.isPresent() && !existingBilling.get().getId().equals(id)) {
                    throw new IllegalStateException("El número de factura ya está en uso");
                }
                billing.setBillNumber(billNumber);
            }

            // Step 3: Update billing rate if changed
            if (billingRateId != null &&
                    (billing.getBillingRate() == null || !billing.getBillingRate().getId().equals(billingRateId))) {
                BillingRate billingRate = billingRateService.findById(billingRateId)
                        .orElseThrow(() -> new EntityNotFoundException("Billing rate not found"));
                billing.setBillingRate(billingRate);

                // Update all items with the new rate
                for (BillingItem item : billing.getItems()) {
                    item.setRateApplied(billingRate.getRatePerKwh());
                    item.setAmount(billingRate.getRatePerKwh().multiply(BigDecimal.valueOf(item.getConsumption())));
                }
            }

            // Step 4: Update discount amount
            if (discountAmount != null) {
                billing.setDiscountAmount(discountAmount);
            }

            // Step 5: Update comments
            billing.setComments(comments);

            // Step 5: Save the billing to persist changes
            billing = billingRepository.save(billing);

            // Step 6: Recalculate billing totals
            recalculateBillingTotals(billing);

            // Step 7: Save the updated billing
            billingRepository.save(billing);
        } catch (Exception e) {
            logger.error("Error updating draft billing {}: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Finalizes a draft billing by changing its status to PENDING.
     * Validates that the billing is in DRAFT status before finalizing.
     *
     * @param id The ID of the billing to finalize
     * @throws IllegalStateException if the billing is not in DRAFT status
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.PAGINATED_BILLINGS_CACHE,
            CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public void finalizeDraft(Long id) {
        try {
            // Step 1: Find and validate the billing
            Billing billing = billingRepository.findActiveById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Billing not found"));

            if (billing.getStatus() != BillingStatus.DRAFT) {
                throw new IllegalStateException("Solo se pueden finalizar facturas en estado borrador");
            }

            // Step 2: Validate billing before finalizing
            validateBillingForFinalization(billing);

            // Step 3: Update the status in a separate transaction
            updateBillingStatus(id, BillingStatus.PENDING);
        } catch (Exception e) {
            logger.error("Error finalizing draft billing {}: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Updates a billing item in a DRAFT billing.
     * Recalculates the billing totals after updating the item.
     *
     * @param billingId   The ID of the billing
     * @param itemId      The ID of the billing item to update
     * @param consumption The new consumption value
     * @param rateApplied The new rate applied
     * @throws IllegalStateException   if the billing is not in DRAFT status
     * @throws EntityNotFoundException if the billing or item is not found
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.BILLING_ITEMS_CACHE,
            CacheConfig.PAGINATED_BILLINGS_CACHE, CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public void updateBillingItem(Long billingId, Long itemId, Float consumption, BigDecimal rateApplied) {
        // Step 1: Find the billing
        Billing billing = billingRepository.findActiveById(billingId)
                .orElseThrow(() -> new EntityNotFoundException("Billing not found"));

        // Step 2: Verify billing is in DRAFT status
        if (billing.getStatus() != BillingStatus.DRAFT) {
            throw new IllegalStateException("Solo se pueden editar facturas en estado borrador");
        }

        // Step 3: Find the billing item
        BillingItem item = null;
        for (BillingItem i : billing.getItems()) {
            if (i.getId().equals(itemId)) {
                item = i;
                break;
            }
        }

        if (item == null) {
            throw new EntityNotFoundException("Billing item not found");
        }

        // Step 4: Update the item
        item.setConsumption(consumption);
        item.setRateApplied(rateApplied);

        // Calculate the new amount
        BigDecimal amount = rateApplied.multiply(BigDecimal.valueOf(consumption));
        item.setAmount(amount);

        // Step 5: Save the billing to persist the item changes
        billing = billingRepository.save(billing);

        // Step 6: Recalculate billing totals
        recalculateBillingTotals(billing);

        // Step 7: Save the updated billing
        billingRepository.save(billing);
    }

    /**
     * Generates billing for a specific customer and period.
     * If a billing already exists for this customer and period, it will be updated.
     *
     * @param customerId    The ID of the customer
     * @param period        The billing period (year and month)
     * @param billingRateId The ID of the billing rate to apply (optional, uses default if null)
     * @return The generated or updated billing
     * @throws IllegalArgumentException if the period is the current month or a future month
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.BILLING_ITEMS_CACHE,
            CacheConfig.PAGINATED_BILLINGS_CACHE, CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public Billing generateBilling(Long customerId, YearMonth period, Long billingRateId) {
        try {
            // Validate that the period is not the current month or a future month
            YearMonth currentMonth = YearMonth.now();
            if (period.compareTo(currentMonth) >= 0) {
                throw new IllegalArgumentException("Solo se pueden generar facturas para meses completos (anteriores al mes actual). " +
                        "No se pueden generar facturas para el mes actual o meses futuros.");
            }

            // PHASE 1: Create or update the billing entity without items
            Billing billing = createOrUpdateBillingEntity(customerId, period, billingRateId);

            // PHASE 2: Add items in a separate transaction
            return addBillingItemsAndCalculateTotals(billing.getId(), customerId, period);
        } catch (Exception e) {
            logger.error("Error generating billing: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Generates billing for all customers for a specific period with a specific billing rate.
     *
     * @param period             The billing period (year and month)
     * @param billingRateId      The ID of the billing rate to apply (optional, uses default if null)
     * @param startingBillNumber The starting bill number for the batch (optional, uses next available if null)
     * @return The list of generated billings
     * @throws IllegalArgumentException if the period is the current month or a future month
     */
    @CacheEvict(value = {CacheConfig.BILLINGS_CACHE, CacheConfig.BILLING_ITEMS_CACHE,
            CacheConfig.PAGINATED_BILLINGS_CACHE, CacheConfig.FILTERED_BILLINGS_CACHE}, allEntries = true)
    public List<Billing> generateBillingForAllCustomers(YearMonth period, Long billingRateId, Long startingBillNumber) {
        // Validate that the period is not the current month or a future month
        YearMonth currentMonth = YearMonth.now();
        if (period.compareTo(currentMonth) >= 0) {
            throw new IllegalArgumentException("Solo se pueden generar facturas para meses completos (anteriores al mes actual). " +
                    "No se pueden generar facturas para el mes actual o meses futuros.");
        }

        // Step 1: Get all customers
        List<Customer> customers = customerService.findAll();
        List<Billing> generatedBillings = new ArrayList<>();

        // Step 2: Get the starting bill number for the batch
        Long nextBillNumber = startingBillNumber != null ? startingBillNumber : getNextBillNumber();

        // Step 3: Get the billing rate
        BillingRate billingRate;
        if (billingRateId != null) {
            billingRate = billingRateService.findById(billingRateId)
                    .orElseThrow(() -> new EntityNotFoundException("Billing rate not found"));
        } else {
            billingRate = billingRateService.findDefaultRate()
                    .orElseThrow(() -> new IllegalStateException("No default billing rate found"));
        }

        // Step 4: Process each customer
        for (Customer customer : customers) {
            try {
                // Skip customers with no spots
                if (customer.getSpots() == null || customer.getSpots().isEmpty()) {
                    logger.info("Skipping customer {} ({} {}) - no spots assigned",
                            customer.getId(), customer.getFirstName(), customer.getLastName());
                    continue;
                }

                // Skip customers with only soft-deleted spots
                long activeSpotCount = customer.getSpots().stream()
                        .filter(spot -> spot.getDeletedAt() == null)
                        .count();

                if (activeSpotCount == 0) {
                    logger.info("Skipping customer {} ({} {}) - no active spots",
                            customer.getId(), customer.getFirstName(), customer.getLastName());
                    continue;
                }

                logger.info("Generating billing for customer {} ({} {}) with {} active spots",
                        customer.getId(), customer.getFirstName(), customer.getLastName(), activeSpotCount);

                // Generate the billing
                Billing billing;
                try {
                    billing = generateBilling(customer.getId(), period, billingRate.getId());
                } catch (Exception e) {
                    logger.error("Error in generateBilling for customer {} ({} {}): {}",
                            customer.getId(), customer.getFirstName(), customer.getLastName(), e.getMessage(), e);
                    continue; // Skip to next customer
                }

                if (billing == null) {
                    logger.error("Generated billing is null for customer {} ({} {})",
                            customer.getId(), customer.getFirstName(), customer.getLastName());
                    continue; // Skip to next customer
                }

                // For batch generation, ensure sequential bill numbers
                try {
                    if (generatedBillings.isEmpty()) {
                        // First billing - use the specified starting bill number
                        if (!billing.getBillNumber().equals(nextBillNumber)) {
                            billing.setBillNumber(nextBillNumber);
                            billing = billingRepository.save(billing);
                        }
                    } else {
                        // Subsequent billings - use sequential numbers
                        Billing lastBilling = generatedBillings.getLast();
                        Long sequentialBillNumber = lastBilling.getBillNumber() + 1;
                        billing.setBillNumber(sequentialBillNumber);
                        billing = billingRepository.save(billing);
                    }

                    generatedBillings.add(billing);
                    logger.info("Successfully generated billing {} with {} items",
                            billing.getId(), billing.getItems().size());
                } catch (Exception e) {
                    logger.error("Error updating bill number for customer {} ({} {}): {}",
                            customer.getId(), customer.getFirstName(), customer.getLastName(), e.getMessage(), e);
                }
            } catch (Exception e) {
                // Log error and continue with next customer
                logger.error("Unexpected error for customer {} ({} {}): {}",
                        customer.getId(), customer.getFirstName(), customer.getLastName(), e.getMessage(), e);
            }
        }

        return generatedBillings;
    }

    /**
     * Validates a billing before finalizing it from DRAFT to PENDING status.
     *
     * @param billing The billing to validate
     * @throws IllegalStateException if validation fails
     */
    private void validateBillingForFinalization(Billing billing) {
        // Check if billing has items
        if (billing.getItems() == null || billing.getItems().isEmpty()) {
            throw new IllegalStateException("La factura debe tener al menos un ítem");
        }

        // Check if billing has a customer
        if (billing.getCustomer() == null) {
            throw new IllegalStateException("La factura debe tener un cliente asignado");
        }

        // Check if billing has a billing rate
        if (billing.getBillingRate() == null) {
            throw new IllegalStateException("La factura debe tener una tarifa asignada");
        }

        // Check if billing has a bill number
        if (billing.getBillNumber() == null) {
            throw new IllegalStateException("La factura debe tener un número asignado");
        }
    }

    /**
     * Recalculates the billing totals based on its items.
     *
     * @param billing The billing to recalculate
     */
    private void recalculateBillingTotals(Billing billing) {
        // Calculate total consumption
        float totalConsumption = 0f;
        BigDecimal consumptionAmount = BigDecimal.ZERO;

        for (BillingItem item : billing.getItems()) {
            totalConsumption += item.getConsumption();
            consumptionAmount = consumptionAmount.add(item.getAmount());
        }

        billing.setTotalConsumption(totalConsumption);
        billing.setConsumptionAmount(consumptionAmount);

        // Fixed fee from billing rate
        BigDecimal fixedFeeAmount = billing.getBillingRate().getFixedMonthlyFee();
        billing.setFixedFeeAmount(fixedFeeAmount);

        // Calculate subtotal (consumption + fixed fee)
        BigDecimal subtotal = consumptionAmount.add(fixedFeeAmount);

        // Apply discount if any
        BigDecimal discountAmount = billing.getDiscountAmount() != null ? billing.getDiscountAmount() : BigDecimal.ZERO;
        if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
            // Ensure discount doesn't exceed subtotal
            if (discountAmount.compareTo(subtotal) > 0) {
                discountAmount = subtotal;
                billing.setDiscountAmount(subtotal);
            }
            subtotal = subtotal.subtract(discountAmount);
        }

        // Calculate tax (21% VAT)
        BigDecimal taxRate = new BigDecimal("0.21");
        BigDecimal taxAmount = subtotal.multiply(taxRate).setScale(4, RoundingMode.HALF_UP);
        billing.setTaxAmount(taxAmount);

        // Calculate total amount
        BigDecimal totalAmount = subtotal.add(taxAmount);
        billing.setTotalAmount(totalAmount);
    }

    /**
     * Calculates the consumption for a spot during a specific period.
     *
     * @param spot   The spot
     * @param period The period (year and month)
     * @return The total consumption in kWh
     */
    private float calculateSpotConsumption(Spot spot, YearMonth period) {
        return consumptionCalculationService.calculateMonthlyConsumption(spot.getId(), period);
    }

    /**
     * Phase 1: Creates or updates a billing entity without items.
     */
    protected Billing createOrUpdateBillingEntity(Long customerId, YearMonth period, Long billingRateId) {
        // Find the customer
        Customer customer = customerService.findById(customerId)
                .orElseThrow(() -> new EntityNotFoundException("Customer not found"));

        // Get the billing rate
        BillingRate billingRate;
        if (billingRateId != null) {
            billingRate = billingRateService.findById(billingRateId)
                    .orElseThrow(() -> new EntityNotFoundException("Billing rate not found"));
        } else {
            billingRate = billingRateService.findDefaultRate()
                    .orElseThrow(() -> new IllegalStateException("No default billing rate found"));
        }

        // Check if billing already exists for this customer and period
        Optional<Billing> existingBillingOpt = billingRepository.findByPeriodAndCustomerId(period, customerId);

        Billing billing;

        if (existingBillingOpt.isPresent()) {
            // Update existing billing
            billing = existingBillingOpt.get();
            billing.setBillingRate(billingRate);

            // We'll clear items in the next phase
        } else {
            // Create new billing
            billing = new Billing(customer, period);
            billing.setBillNumber(getNextBillNumber());
            billing.setStatus(BillingStatus.DRAFT);
            billing.setBillingRate(billingRate);
        }

        // Save and return the billing entity
        return billingRepository.save(billing);
    }

    /**
     * Phase 2: Adds items to a billing and calculates totals.
     */
    protected Billing addBillingItemsAndCalculateTotals(Long billingId, Long customerId, YearMonth period) {
        // Retrieve the billing entity
        Billing billing = billingRepository.findById(billingId)
                .orElseThrow(() -> new EntityNotFoundException("Billing not found"));

        // Retrieve the customer
        Customer customer = customerService.findById(customerId)
                .orElseThrow(() -> new EntityNotFoundException("Customer not found"));

        // Get the billing rate from the billing entity
        BillingRate billingRate = billing.getBillingRate();
        if (billingRate == null) {
            throw new IllegalStateException("Billing rate not found in billing entity");
        }

        // Clear existing items if any
        if (billing.getItems() != null && !billing.getItems().isEmpty()) {
            // Delete items directly from the repository to avoid Hibernate issues
            billing.getItems().forEach(item -> {
                // We need to detach the item from the billing first
                item.setBilling(null);
            });
            billing.getItems().clear();
            billing = billingRepository.save(billing);
        }

        // Validate spots
        List<Spot> allSpots = customer.getSpots();
        if (allSpots == null || allSpots.isEmpty()) {
            throw new IllegalStateException("Customer has no spots assigned");
        }

        // Filter out soft-deleted spots
        List<Spot> activeSpots = allSpots.stream()
                .filter(spot -> spot.getDeletedAt() == null)
                .toList();

        if (activeSpots.isEmpty()) {
            throw new IllegalStateException("Customer has no active spots assigned");
        }

        logger.info("Customer {} has {} total spots and {} active spots",
                customer.getId(), allSpots.size(), activeSpots.size());

        // Calculate consumption and create items
        float totalConsumption = 0f;

        for (Spot spot : activeSpots) {
            float spotConsumption = calculateSpotConsumption(spot, period);
            totalConsumption += spotConsumption;

            // Create and save billing item
            BillingItem item = new BillingItem(spot, spotConsumption, billingRate.getRatePerKwh());
            item.setBilling(billing);

            // Add to the billing's items collection
            if (billing.getItems() == null) {
                billing.setItems(new ArrayList<>());
            }
            billing.getItems().add(item);
        }

        // Save the billing with items
        billing = billingRepository.save(billing);

        // Calculate billing amounts
        billing.setTotalConsumption(totalConsumption);

        // Fixed fee amount
        BigDecimal fixedFeeAmount = billingRate.getFixedMonthlyFee();
        billing.setFixedFeeAmount(fixedFeeAmount);

        // Consumption amount
        BigDecimal consumptionAmount = billingRate.getRatePerKwh()
                .multiply(BigDecimal.valueOf(totalConsumption))
                .setScale(2, RoundingMode.HALF_UP);
        billing.setConsumptionAmount(consumptionAmount);

        // Subtotal (fixed fee + consumption)
        BigDecimal subtotal = fixedFeeAmount.add(consumptionAmount);

        // Tax amount
        BigDecimal taxAmount = subtotal.multiply(TAX_RATE).setScale(2, RoundingMode.HALF_UP);
        billing.setTaxAmount(taxAmount);

        // Total amount
        BigDecimal totalAmount = subtotal.add(taxAmount).setScale(2, RoundingMode.HALF_UP);
        billing.setTotalAmount(totalAmount);

        // Save and return the final billing
        return billingRepository.save(billing);
    }
}
