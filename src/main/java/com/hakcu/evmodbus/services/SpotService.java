package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.config.CacheConfig;
import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.repositories.SpotRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class SpotService {
    private final SpotRepository spotRepository;

    public SpotService(SpotRepository spotRepository) {
        this.spotRepository = spotRepository;
    }

    @Cacheable(CacheConfig.SPOTS_CACHE)
    public List<Spot> findAll() {
        return spotRepository.findAllActive();
    }

    @Cacheable(value = CacheConfig.SPOTS_CACHE, key = "#id")
    public Optional<Spot> findById(Long id) {
        return spotRepository.findActiveById(id);
    }

    @CacheEvict(value = {CacheConfig.SPOTS_CACHE, CacheConfig.LATEST_READINGS_CACHE}, allEntries = true)
    public Spot save(Spot spot) {
        return spotRepository.save(spot);
    }

    @CacheEvict(value = {CacheConfig.SPOTS_CACHE, CacheConfig.LATEST_READINGS_CACHE}, allEntries = true)
    public Spot delete(Long id) {
        Spot spot = spotRepository.findActiveById(id).orElseThrow(() -> new EntityNotFoundException("Spot not found"));
        spot.setDeletedAt(LocalDateTime.now());
        return spotRepository.save(spot);
    }
}
