package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.config.CacheConfig;
import com.hakcu.evmodbus.entities.BillingRate;
import com.hakcu.evmodbus.repositories.BillingRateRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class BillingRateService {
    private final BillingRateRepository billingRateRepository;

    public BillingRateService(BillingRateRepository billingRateRepository) {
        this.billingRateRepository = billingRateRepository;
    }

    @Cacheable(CacheConfig.BILLING_RATES_CACHE)
    public List<BillingRate> findAll() {
        return billingRateRepository.findAllActive();
    }

    @Cacheable(value = CacheConfig.BILLING_RATES_CACHE, key = "#id")
    public Optional<BillingRate> findById(Long id) {
        return billingRateRepository.findActiveById(id);
    }

    @Cacheable(value = CacheConfig.BILLING_RATES_CACHE, key = "'default'")
    public Optional<BillingRate> findDefaultRate() {
        return billingRateRepository.findDefaultRate();
    }

    @CacheEvict(value = CacheConfig.BILLING_RATES_CACHE, allEntries = true)
    public BillingRate save(BillingRate billingRate) {
        // If this rate is set as default, unset any existing default
        if (billingRate.isDefault()) {
            billingRateRepository.findDefaultRate().ifPresent(existingDefault -> {
                if (!existingDefault.getId().equals(billingRate.getId())) {
                    existingDefault.setDefault(false);
                    billingRateRepository.save(existingDefault);
                }
            });
        }
        return billingRateRepository.save(billingRate);
    }

    @CacheEvict(value = CacheConfig.BILLING_RATES_CACHE, allEntries = true)
    public BillingRate delete(Long id) {
        BillingRate billingRate = billingRateRepository.findActiveById(id)
                .orElseThrow(() -> new EntityNotFoundException("Billing rate not found"));
        billingRate.setDeletedAt(LocalDateTime.now());
        return billingRateRepository.save(billingRate);
    }

    /**
     * Checks if any active billing rates exist in the system.
     *
     * @return true if at least one active billing rate exists, false otherwise
     */
    @Cacheable(value = CacheConfig.BILLING_RATES_CACHE, key = "'existsAny'")
    public boolean existsAnyBillingRate() {
        List<BillingRate> rates = findAll();
        return !rates.isEmpty();
    }
}
