package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.services.consumption.ConsumptionCalculationService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class StatisticsService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter DATE_PARSER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final SpotService spotService;
    private final ChartService chartService;
    private final ConsumptionCalculationService consumptionCalculationService;

    public StatisticsService(SpotService spotService, ChartService chartService,
                           ConsumptionCalculationService consumptionCalculationService) {
        this.spotService = spotService;
        this.chartService = chartService;
        this.consumptionCalculationService = consumptionCalculationService;
    }

    public Map<String, Float> getHourlyConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return consumptionCalculationService.getHourlyConsumption(spotId, startDateTime, endDateTime);
    }

    public Map<String, Float> getDailyConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return consumptionCalculationService.getDailyConsumption(spotId, startDateTime, endDateTime);
    }

    public Map<String, Float> getMonthlyConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return consumptionCalculationService.getMonthlyConsumption(spotId, startDateTime, endDateTime);
    }

    public Float getTotalConsumption(Long spotId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return consumptionCalculationService.calculateTotalConsumption(spotId, startDateTime, endDateTime);
    }

    public String getFormattedDayForTitle(String date) {
        try {
            LocalDate localDate = LocalDate.parse(date, DATE_PARSER);
            return localDate.format(DATE_FORMATTER);
        } catch (Exception e) {
            return date;
        }
    }

    public String generateLineChartSvg(Map<String, Float> data, int width, int height, String title, String yAxisLabel) {
        return chartService.generateLineChartSvg(data, width, height, title, yAxisLabel);
    }

    public String generateBarChartSvg(Map<String, Float> data, int width, int height, String title, String yAxisLabel) {
        return chartService.generateBarChartSvg(data, width, height, title, yAxisLabel);
    }

    public String generatePieChartSvg(Map<String, Float> data, int width, int height, String title) {
        return chartService.generatePieChartSvg(data, width, height, title);
    }

    public List<Spot> getAllSpots() {
        return spotService.findAll();
    }

    public Optional<Spot> getSpotById(Long id) {
        return spotService.findById(id);
    }


}