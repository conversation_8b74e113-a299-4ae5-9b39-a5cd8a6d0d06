package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.entities.Floor;
import com.hakcu.evmodbus.repositories.FloorRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class FloorService {
    private final FloorRepository floorRepository;

    public FloorService(FloorRepository floorRepository) {
        this.floorRepository = floorRepository;
    }

    public List<Floor> findAll() {
        return floorRepository.findAllActive();
    }

    public Optional<Floor> findById(Long id) {
        return floorRepository.findActiveById(id);
    }

    public Floor save(Floor floor) {
        return floorRepository.save(floor);
    }

    public void delete(Long id) {
        Floor floor = floorRepository.findActiveById(id).orElseThrow(() -> new EntityNotFoundException("Floor not found"));
        floor.setDeletedAt(LocalDateTime.now());
        floorRepository.save(floor);
    }
}
