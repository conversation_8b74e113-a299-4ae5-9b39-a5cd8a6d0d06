package com.hakcu.evmodbus.services;

import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Service for generating SVG charts.
 * Handles all chart generation logic separate from data processing.
 * Generates responsive SVG charts that adapt to different screen sizes.
 */
@Service
public class ChartService {

    private static final String CHART_COLOR = "#007bff"; // Bootstrap primary blue color
    private static final String CHART_FONT_FAMILY = "Arial, sans-serif";

    // Common chart dimensions
    private static final int DEFAULT_TOP_MARGIN = 50;
    private static final int DEFAULT_BOTTOM_MARGIN = 80;
    private static final int DEFAULT_SIDE_MARGIN = 50;
    private static final int DEFAULT_Y_LABELS = 5;
    private static final int TITLE_Y_POSITION = 20;
    private static final int Y_AXIS_LABEL_OFFSET = 40;

    /**
     * Generates SVG data for a bar chart.
     *
     * @param data Map of labels to values.
     * @param width Width of the chart.
     * @param height Height of the chart.
     * @param title Title of the chart.
     * @param yAxisLabel Label for the Y axis.
     * @return SVG string.
     */
    public String generateBarChartSvg(Map<String, Float> data, int width, int height, String title, String yAxisLabel) {
        if (data.isEmpty()) {
            return generateEmptyChartSvg(width, height, title);
        }

        // Determine chart type and dimensions
        ChartType chartType = detectChartType(data);
        int topMargin = DEFAULT_TOP_MARGIN;
        int bottomMargin = calculateBottomMargin(chartType);
        int sideMargin = DEFAULT_SIDE_MARGIN;
        int chartWidth = width - 2 * sideMargin;
        int chartHeight = height - topMargin - bottomMargin;

        // Calculate value range
        float minValue = 0; // Start from 0 for bar charts
        float maxValue = calculateMaxValue(data);

        // Generate SVG
        StringBuilder svg = createSvgBuilder(width, height);
        addTitle(svg, title, width);
        addAxes(svg, width, height, topMargin, bottomMargin, sideMargin);
        addXAxisLabels(svg, data, chartType, height, bottomMargin, sideMargin, chartWidth);
        addYAxisLabels(svg, minValue, maxValue, height, bottomMargin, chartHeight, sideMargin);
        addYAxisTitle(svg, yAxisLabel, height, sideMargin);
        addBarChartData(svg, data, minValue, maxValue, height, bottomMargin, chartHeight, sideMargin, chartWidth);

        svg.append("</svg>");
        return svg.toString();
    }

    /**
     * Calculates the maximum value for the chart with appropriate minimum threshold.
     *
     * @param data Map of labels to values.
     * @return Maximum value with padding.
     */
    private float calculateMaxValue(Map<String, Float> data) {
        float maxValue = Collections.max(data.values());

        // Ensure there's a range to display and set a minimum y-axis value of 5
        // This prevents small readings (0.0x) from displaying as full-height bars
        if (maxValue < 5.0f) {
            maxValue = 5.0f;
        }

        // Add some padding to the value range
        float padding = maxValue * 0.1f;
        return maxValue + padding;
    }

    /**
     * Adds X-axis labels to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param data Map of labels to values.
     * @param chartType The type of chart being rendered.
     * @param height Height of the chart area.
     * @param bottomMargin Bottom margin.
     * @param sideMargin Side margin.
     * @param chartWidth Width of the chart content area.
     */
    private void addXAxisLabels(StringBuilder svg, Map<String, Float> data, ChartType chartType,
                               int height, int bottomMargin, int sideMargin, int chartWidth) {
        List<String> labels = new ArrayList<>(data.keySet());
        int barWidth = chartWidth / labels.size();

        // Determine label step based on chart type
        int labelStep = (chartType == ChartType.HOURLY || chartType == ChartType.DAILY)
                       ? 1 : Math.max(1, labels.size() / 10);

        for (int i = 0; i < labels.size(); i += labelStep) {
            String label = labels.get(i);
            int x = sideMargin + (i * barWidth) + (barWidth / 2);

            if (chartType == ChartType.HOURLY || chartType == ChartType.DAILY) {
                // For hourly and daily charts, rotate labels diagonally
                svg.append("  <text x=\"").append(x)
                   .append("\" y=\"").append(height - bottomMargin + 15)
                   .append("\" text-anchor=\"end\" font-family=\"").append(CHART_FONT_FAMILY)
                   .append("\" font-size=\"9\" transform=\"rotate(-45,")
                   .append(x).append(",").append(height - bottomMargin + 15)
                   .append(")\" class=\"axis-label\">").append(label).append("</text>\n");
            } else {
                // For other charts, keep labels horizontal
                svg.append("  <text x=\"").append(x)
                   .append("\" y=\"").append(height - bottomMargin + 20)
                   .append("\" text-anchor=\"middle\" font-family=\"").append(CHART_FONT_FAMILY)
                   .append("\" font-size=\"10\" class=\"axis-label\">").append(label).append("</text>\n");
            }

            // Add tick mark
            svg.append("  <line x1=\"").append(x)
               .append("\" y1=\"").append(height - bottomMargin)
               .append("\" x2=\"").append(x)
               .append("\" y2=\"").append(height - bottomMargin + 5)
               .append("\" stroke=\"black\" />\n");
        }
    }

    /**
     * Adds bar chart data to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param data Map of labels to values.
     * @param minValue Minimum value on the Y-axis.
     * @param maxValue Maximum value on the Y-axis.
     * @param height Height of the chart area.
     * @param bottomMargin Bottom margin.
     * @param chartHeight Height of the chart content area.
     * @param sideMargin Side margin.
     * @param chartWidth Width of the chart content area.
     */
    private void addBarChartData(StringBuilder svg, Map<String, Float> data, float minValue, float maxValue,
                                int height, int bottomMargin, int chartHeight, int sideMargin, int chartWidth) {
        List<String> labels = new ArrayList<>(data.keySet());
        int barWidth = chartWidth / labels.size();

        for (int i = 0; i < labels.size(); i++) {
            String label = labels.get(i);
            float value = data.get(label);

            int x = sideMargin + (i * barWidth);
            // Calculate bar height, ensuring a minimum visible height for non-zero values
            int barHeight = 0;
            if (maxValue > minValue) {
                barHeight = (int)((value - minValue) / (maxValue - minValue) * chartHeight);
            }

            // Ensure bars with very small values are still visible (minimum 1 pixel height)
            if (value > 0 && barHeight < 1) {
                barHeight = 1;
            }
            int y = height - bottomMargin - barHeight;

            svg.append("  <rect x=\"").append(x)
               .append("\" y=\"").append(y)
               .append("\" width=\"").append(barWidth - 2)
               .append("\" height=\"").append(barHeight)
               .append("\" fill=\"").append(CHART_COLOR)
               .append("\" class=\"chart-bar\" />\n");
        }
    }

    /**
     * Generates SVG data for a pie chart.
     *
     * @param data Map of labels to values.
     * @param width Width of the chart.
     * @param height Height of the chart.
     * @param title Title of the chart.
     * @return SVG string.
     */
    public String generatePieChartSvg(Map<String, Float> data, int width, int height, String title) {
        if (data.isEmpty()) {
            return generateEmptyChartSvg(width, height, title);
        }

        // Chart dimensions
        int centerX = width / 2;
        int centerY = height / 2;
        int radius = Math.min(centerX, centerY) - 60;

        // Calculate total value
        float total = calculateTotal(data);

        // Generate SVG
        StringBuilder svg = createSvgBuilder(width, height);
        addTitle(svg, title, width);
        addPieSlices(svg, data, total, centerX, centerY, radius);

        svg.append("</svg>");
        return svg.toString();
    }

    /**
     * Calculates the total sum of all values in the data map.
     *
     * @param data Map of labels to values.
     * @return Total sum of all values.
     */
    private float calculateTotal(Map<String, Float> data) {
        return data.values().stream().reduce(0f, Float::sum);
    }

    /**
     * Adds pie slices to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param data Map of labels to values.
     * @param total Total sum of all values.
     * @param centerX X-coordinate of the pie center.
     * @param centerY Y-coordinate of the pie center.
     * @param radius Radius of the pie.
     */
    private void addPieSlices(StringBuilder svg, Map<String, Float> data, float total,
                             int centerX, int centerY, int radius) {
        float startAngle = 0;
        int colorIndex = 0;

        List<String> labels = new ArrayList<>(data.keySet());
        for (String label : labels) {
            float value = data.get(label);

            // Calculate slice angles
            float sliceAngle = 360 * (value / total);
            float endAngle = startAngle + sliceAngle;

            // Add the slice
            addPieSlice(svg, startAngle, endAngle, value, label, centerX, centerY, radius, colorIndex, labels.size());

            startAngle = endAngle;
            colorIndex++;
        }
    }

    /**
     * Adds a single pie slice to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param startAngle Start angle of the slice in degrees.
     * @param endAngle End angle of the slice in degrees.
     * @param value Value represented by the slice.
     * @param label Label for the slice.
     * @param centerX X-coordinate of the pie center.
     * @param centerY Y-coordinate of the pie center.
     * @param radius Radius of the pie.
     * @param colorIndex Index for color variation.
     * @param totalSlices Total number of slices for opacity calculation.
     */
    private void addPieSlice(StringBuilder svg, float startAngle, float endAngle, float value, String label,
                            int centerX, int centerY, int radius, int colorIndex, int totalSlices) {
        // Convert angles to radians
        double startRad = Math.toRadians(startAngle);
        double endRad = Math.toRadians(endAngle);
        float sliceAngle = endAngle - startAngle;

        // Calculate points
        int x1 = centerX + (int) (radius * Math.sin(startRad));
        int y1 = centerY - (int) (radius * Math.cos(startRad));
        int x2 = centerX + (int) (radius * Math.sin(endRad));
        int y2 = centerY - (int) (radius * Math.cos(endRad));

        // Determine if the slice is more than 180 degrees
        int largeArcFlag = (sliceAngle > 180) ? 1 : 0;

        // Generate path
        String path = "M" + centerX + "," + centerY +
                " L" + x1 + "," + y1 +
                " A" + radius + "," + radius + " 0 " + largeArcFlag + " 1 " + x2 + "," + y2 +
                " Z";

        // Add slice - use a single color with varying opacity for pie slices
        float opacity = 1.0f - (0.5f * colorIndex / totalSlices);
        opacity = Math.max(0.3f, opacity); // Ensure minimum opacity of 0.3
        svg.append("  <path d=\"").append(path)
           .append("\" fill=\"").append(CHART_COLOR)
           .append("\" fill-opacity=\"").append(opacity)
           .append("\" stroke=\"white\" stroke-width=\"1\" class=\"pie-slice\" />\n");

        // Add label and connecting line
        addPieSliceLabel(svg, startAngle, sliceAngle, value, label, centerX, centerY, radius);
    }

    /**
     * Adds a label and connecting line for a pie slice.
     *
     * @param svg StringBuilder to append to.
     * @param startAngle Start angle of the slice in degrees.
     * @param sliceAngle Angle size of the slice in degrees.
     * @param value Value represented by the slice.
     * @param label Label for the slice.
     * @param centerX X-coordinate of the pie center.
     * @param centerY Y-coordinate of the pie center.
     * @param radius Radius of the pie.
     */
    private void addPieSliceLabel(StringBuilder svg, float startAngle, float sliceAngle, float value, String label,
                                 int centerX, int centerY, int radius) {
        // Calculate midpoint of the slice for label placement
        double midRad = Math.toRadians(startAngle + sliceAngle / 2);

        // Calculate label line endpoints
        int innerX = centerX + (int) (radius * Math.sin(midRad));
        int innerY = centerY - (int) (radius * Math.cos(midRad));
        int labelX = centerX + (int) ((radius + 20) * Math.sin(midRad));
        int labelY = centerY - (int) ((radius + 20) * Math.cos(midRad));

        // Calculate text position
        int textX = centerX + (int) ((radius + 40) * Math.sin(midRad));
        int textY = centerY - (int) ((radius + 40) * Math.cos(midRad));

        // Add connecting line
        svg.append("  <line x1=\"").append(innerX)
           .append("\" y1=\"").append(innerY)
           .append("\" x2=\"").append(labelX)
           .append("\" y2=\"").append(labelY)
           .append("\" stroke=\"").append(CHART_COLOR)
           .append("\" stroke-width=\"1\" class=\"pie-label-line\" />\n");

        // Add text label
        String textAnchor = (textX < centerX) ? "end" : "start";
        svg.append("  <text x=\"").append(textX)
           .append("\" y=\"").append(textY)
           .append("\" text-anchor=\"").append(textAnchor)
           .append("\" font-family=\"").append(CHART_FONT_FAMILY)
           .append("\" font-size=\"10\" class=\"pie-label\">").append(label)
           .append(" (").append(String.format("%.1f", value)).append(")</text>\n");
    }

    /**
     * Generates SVG data for a line chart.
     *
     * @param data Map of labels to values.
     * @param width Width of the chart.
     * @param height Height of the chart.
     * @param title Title of the chart.
     * @param yAxisLabel Label for the Y axis.
     * @return SVG string.
     */
    public String generateLineChartSvg(Map<String, Float> data, int width, int height, String title, String yAxisLabel) {
        if (data.isEmpty()) {
            return generateEmptyChartSvg(width, height, title);
        }

        // Determine chart type and dimensions
        ChartType chartType = detectChartType(data);
        int topMargin = DEFAULT_TOP_MARGIN;
        int bottomMargin = calculateBottomMargin(chartType);
        int sideMargin = DEFAULT_SIDE_MARGIN;
        int chartWidth = width - 2 * sideMargin;
        int chartHeight = height - topMargin - bottomMargin;

        // Calculate value range for line chart (can start below zero)
        ValueRange valueRange = calculateLineChartValueRange(data);
        float minValue = valueRange.min;
        float maxValue = valueRange.max;

        // Generate SVG
        StringBuilder svg = createSvgBuilder(width, height);
        addTitle(svg, title, width);
        addAxes(svg, width, height, topMargin, bottomMargin, sideMargin);
        addLineChartXAxisLabels(svg, data, height, bottomMargin, sideMargin, chartWidth);
        addYAxisLabels(svg, minValue, maxValue, height, bottomMargin, chartHeight, sideMargin);
        addYAxisTitle(svg, yAxisLabel, height, sideMargin);
        addLineChartData(svg, data, minValue, maxValue, height, bottomMargin, chartHeight, sideMargin, chartWidth);

        svg.append("</svg>");
        return svg.toString();
    }

    /**
         * Value range class to hold min and max values.
         */
        private record ValueRange(float min, float max) {
    }

    /**
     * Calculates the value range for a line chart with appropriate padding.
     *
     * @param data Map of labels to values.
     * @return ValueRange object with min and max values.
     */
    private ValueRange calculateLineChartValueRange(Map<String, Float> data) {
        float minValue = Collections.min(data.values());
        float maxValue = Collections.max(data.values());

        // Ensure there's a range to display
        if (maxValue - minValue < 0.001f) {
            maxValue = minValue + 1.0f;
        }

        // Add some padding to the value range
        float padding = (maxValue - minValue) * 0.1f;
        minValue = Math.max(0, minValue - padding);
        maxValue = maxValue + padding;

        return new ValueRange(minValue, maxValue);
    }

    /**
     * Adds X-axis labels for a line chart.
     *
     * @param svg StringBuilder to append to.
     * @param data Map of labels to values.
     * @param height Height of the chart area.
     * @param bottomMargin Bottom margin.
     * @param sideMargin Side margin.
     * @param chartWidth Width of the chart content area.
     */
    private void addLineChartXAxisLabels(StringBuilder svg, Map<String, Float> data,
                                        int height, int bottomMargin, int sideMargin, int chartWidth) {
        List<String> labels = new ArrayList<>(data.keySet());
        int labelStep = Math.max(1, labels.size() / 10);

        for (int i = 0; i < labels.size(); i += labelStep) {
            String label = labels.get(i);
            int x = sideMargin + (i * chartWidth / (labels.size() - 1 > 0 ? labels.size() - 1 : 1));

            svg.append("  <text x=\"").append(x)
               .append("\" y=\"").append(height - bottomMargin + 20)
               .append("\" text-anchor=\"middle\" font-family=\"").append(CHART_FONT_FAMILY)
               .append("\" font-size=\"10\" class=\"axis-label\">").append(label).append("</text>\n");

            svg.append("  <line x1=\"").append(x)
               .append("\" y1=\"").append(height - bottomMargin)
               .append("\" x2=\"").append(x)
               .append("\" y2=\"").append(height - bottomMargin + 5)
               .append("\" stroke=\"black\" />\n");
        }
    }

    /**
     * Adds line chart data (line and points) to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param data Map of labels to values.
     * @param minValue Minimum value on the Y-axis.
     * @param maxValue Maximum value on the Y-axis.
     * @param height Height of the chart area.
     * @param bottomMargin Bottom margin.
     * @param chartHeight Height of the chart content area.
     * @param sideMargin Side margin.
     * @param chartWidth Width of the chart content area.
     */
    private void addLineChartData(StringBuilder svg, Map<String, Float> data, float minValue, float maxValue,
                                 int height, int bottomMargin, int chartHeight, int sideMargin, int chartWidth) {
        List<String> labels = new ArrayList<>(data.keySet());
        List<String> points = new ArrayList<>();

        // Generate points for the polyline
        for (int i = 0; i < labels.size(); i++) {
            String label = labels.get(i);
            float value = data.get(label);

            int x = sideMargin + (i * chartWidth / (labels.size() - 1 > 0 ? labels.size() - 1 : 1));
            int y = height - bottomMargin - (int)((value - minValue) / (maxValue - minValue) * chartHeight);

            points.add(x + "," + y);
        }

        // Draw line
        if (points.size() > 1) {
            svg.append("  <polyline points=\"");
            for (String point : points) {
                svg.append(point).append(" ");
            }
            svg.append("\" fill=\"none\" stroke=\"").append(CHART_COLOR)
               .append("\" stroke-width=\"2\" class=\"line-path\" />\n");
        }

        // Draw points
        for (int i = 0; i < labels.size(); i++) {
            String label = labels.get(i);
            float value = data.get(label);

            int x = sideMargin + (i * chartWidth / (labels.size() - 1 > 0 ? labels.size() - 1 : 1));
            int y = height - bottomMargin - (int)((value - minValue) / (maxValue - minValue) * chartHeight);

            svg.append("  <circle cx=\"").append(x)
               .append("\" cy=\"").append(y)
               .append("\" r=\"3\" fill=\"").append(CHART_COLOR)
               .append("\" class=\"data-point\" />\n");
        }
    }

    /**
     * Creates a new SVG builder with common attributes.
     *
     * @param width Width of the SVG.
     * @param height Height of the SVG.
     * @return StringBuilder with SVG opening tag and common attributes.
     */
    private StringBuilder createSvgBuilder(int width, int height) {
        StringBuilder svg = new StringBuilder();
        svg.append("<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 ").append(width).append(" ").append(height).append("\" ");
        svg.append("width=\"100%\" height=\"100%\" preserveAspectRatio=\"xMidYMid meet\" ");
        svg.append("style=\"max-width:100%; height:auto; display:block;\">\n");
        return svg;
    }

    /**
     * Adds a title to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param title Title text.
     * @param width Width of the SVG for centering.
     */
    private void addTitle(StringBuilder svg, String title, int width) {
        svg.append("  <text x=\"").append(width / 2)
           .append("\" y=\"").append(TITLE_Y_POSITION)
           .append("\" text-anchor=\"middle\" font-family=\"").append(CHART_FONT_FAMILY)
           .append("\" font-size=\"16\" class=\"chart-title\">").append(title).append("</text>\n");
    }

    /**
     * Adds X and Y axes to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param width Width of the chart area.
     * @param height Height of the chart area.
     * @param topMargin Top margin.
     * @param bottomMargin Bottom margin.
     * @param sideMargin Side margin.
     */
    private void addAxes(StringBuilder svg, int width, int height, int topMargin, int bottomMargin, int sideMargin) {
        // X-axis
        svg.append("  <line x1=\"").append(sideMargin)
           .append("\" y1=\"").append(height - bottomMargin)
           .append("\" x2=\"").append(width - sideMargin)
           .append("\" y2=\"").append(height - bottomMargin)
           .append("\" stroke=\"black\" />\n");

        // Y-axis
        svg.append("  <line x1=\"").append(sideMargin)
           .append("\" y1=\"").append(topMargin)
           .append("\" x2=\"").append(sideMargin)
           .append("\" y2=\"").append(height - bottomMargin)
           .append("\" stroke=\"black\" />\n");
    }

    /**
     * Adds Y-axis labels to the SVG.
     *
     * @param svg          StringBuilder to append to.
     * @param minValue     Minimum value on the Y-axis.
     * @param maxValue     Maximum value on the Y-axis.
     * @param height       Height of the chart area.
     * @param bottomMargin Bottom margin.
     * @param chartHeight  Height of the chart content area.
     * @param sideMargin   Side margin.
     */
    private void addYAxisLabels(StringBuilder svg, float minValue, float maxValue, int height,
                               int bottomMargin, int chartHeight, int sideMargin) {
        for (int i = 0; i <= ChartService.DEFAULT_Y_LABELS; i++) {
            float value = minValue + (maxValue - minValue) * i / ChartService.DEFAULT_Y_LABELS;
            int y = height - bottomMargin - (int)((value - minValue) / (maxValue - minValue) * chartHeight);

            svg.append("  <text x=\"").append(sideMargin - 10)
               .append("\" y=\"").append(y + 5)
               .append("\" text-anchor=\"end\" font-family=\"").append(CHART_FONT_FAMILY)
               .append("\" font-size=\"10\" class=\"axis-label\">").append(String.format("%.1f", value)).append("</text>\n");

            svg.append("  <line x1=\"").append(sideMargin - 5)
               .append("\" y1=\"").append(y)
               .append("\" x2=\"").append(sideMargin)
               .append("\" y2=\"").append(y)
               .append("\" stroke=\"black\" />\n");
        }
    }

    /**
     * Adds a Y-axis title to the SVG.
     *
     * @param svg StringBuilder to append to.
     * @param yAxisLabel Y-axis label text.
     * @param height Height of the chart area.
     * @param sideMargin Side margin.
     */
    private void addYAxisTitle(StringBuilder svg, String yAxisLabel, int height, int sideMargin) {
        if (yAxisLabel != null && !yAxisLabel.isEmpty()) {
            svg.append("  <text x=\"").append(sideMargin - Y_AXIS_LABEL_OFFSET)
               .append("\" y=\"").append(height / 2)
               .append("\" text-anchor=\"middle\" font-family=\"").append(CHART_FONT_FAMILY)
               .append("\" font-size=\"12\" class=\"y-axis-title\" transform=\"rotate(-90,")
               .append(sideMargin - Y_AXIS_LABEL_OFFSET).append(",").append(height / 2)
               .append(")\">").append(yAxisLabel).append("</text>\n");
        }
    }

    /**
     * Detects the chart type based on the data labels.
     *
     * @param data Map of labels to values.
     * @return ChartType enum value.
     */
    private ChartType detectChartType(Map<String, Float> data) {
        if (data.isEmpty()) {
            return ChartType.UNKNOWN;
        }

        String sampleLabel = data.keySet().iterator().next();
        if (sampleLabel.contains(":00 -")) {
            return ChartType.HOURLY;
        } else if (sampleLabel.matches("\\d{2}/\\d{2}")) { // Format: dd/MM
            return ChartType.DAILY;
        } else {
            return ChartType.OTHER;
        }
    }

    /**
     * Calculates the appropriate bottom margin based on chart type.
     *
     * @param chartType The detected chart type.
     * @return The bottom margin value.
     */
    private int calculateBottomMargin(ChartType chartType) {
        return (chartType == ChartType.HOURLY || chartType == ChartType.DAILY)
               ? 100  // More space for rotated labels
               : DEFAULT_BOTTOM_MARGIN;
    }

    /**
     * Enum representing different chart types based on data format.
     */
    private enum ChartType {
        HOURLY,
        DAILY,
        OTHER,
        UNKNOWN
    }

    /**
     * Generates an empty chart SVG when no data is available.
     *
     * @param width Width of the chart.
     * @param height Height of the chart.
     * @param title Title of the chart.
     * @return SVG string.
     */
    private String generateEmptyChartSvg(int width, int height, String title) {
        StringBuilder svg = createSvgBuilder(width, height);
        addTitle(svg, title, width);

        // Add empty state indicator
        svg.append("  <rect x=\"10\" y=\"30\" width=\"").append(width - 20)
           .append("\" height=\"").append(height - 40)
           .append("\" fill=\"none\" stroke=\"#ccc\" stroke-dasharray=\"5,5\" class=\"empty-chart-box\" />\n");

        svg.append("  <text x=\"").append(width / 2)
           .append("\" y=\"").append(height / 2)
           .append("\" text-anchor=\"middle\" font-family=\"").append(CHART_FONT_FAMILY)
           .append("\" font-size=\"14\" class=\"empty-chart-message\">No hay datos disponibles</text>\n");

        svg.append("</svg>");
        return svg.toString();
    }
}
