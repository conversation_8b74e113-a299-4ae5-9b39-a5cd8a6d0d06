package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.config.CacheConfig;
import com.hakcu.evmodbus.entities.Reading;
import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.repositories.ReadingRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ReadingService {

    private final ReadingRepository readingRepository;

    ReadingService(ReadingRepository readingRepository) {
        this.readingRepository = readingRepository;
    }

    @Cacheable(CacheConfig.READINGS_CACHE)
    public List<Reading> findAll() {
        return readingRepository.findAllActive();
    }

    @Cacheable(value = CacheConfig.READINGS_CACHE, key = "'bySpot_' + #id")
    public List<Reading> findAllBySpotId(Long id) {
        return readingRepository.findAllActiveBySpot(id);
    }

    @Cacheable(value = CacheConfig.READINGS_CACHE, key = "#id")
    public Optional<Reading> findById(Long id) {
        return readingRepository.findActiveById(id);
    }

    @CacheEvict(value = {CacheConfig.READINGS_CACHE, CacheConfig.LATEST_READINGS_CACHE, CacheConfig.PAGINATED_READINGS_CACHE, CacheConfig.FILTERED_READINGS_CACHE}, allEntries = true)
    public Reading save(Reading reading) {
        return readingRepository.save(reading);
    }

    @CacheEvict(value = {CacheConfig.READINGS_CACHE, CacheConfig.LATEST_READINGS_CACHE, CacheConfig.PAGINATED_READINGS_CACHE, CacheConfig.FILTERED_READINGS_CACHE}, allEntries = true)
    public Reading delete(Long id) {
        Reading reading = readingRepository.findActiveById(id).orElseThrow(() -> new EntityNotFoundException("Reading not found"));
        reading.setDeletedAt(LocalDateTime.now());
        return readingRepository.save(reading);
    }

    @Cacheable(value = CacheConfig.PAGINATED_READINGS_CACHE, key = "#spotId + '_' + #page + '_' + #size")
    public Page<Reading> findPaginated(Long spotId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return readingRepository.findAllActiveBySpot(spotId, pageable);
    }

    @Cacheable(value = CacheConfig.FILTERED_READINGS_CACHE, key = "#spotId + '_' + #successFilter + '_' + (#startDateTime != null ? #startDateTime.toString() : 'null') + '_' + (#endDateTime != null ? #endDateTime.toString() : 'null') + '_' + #page + '_' + #size")
    public Page<Reading> findPaginatedWithFilters(Long spotId, String successFilter, LocalDateTime startDateTime, LocalDateTime endDateTime, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);

        // Convert success filter string to Boolean (or null for "all")
        Boolean success = null;
        if ("success".equals(successFilter)) {
            success = true;
        } else if ("failed".equals(successFilter)) {
            success = false;
        }

        // Adjust endDateTime to include readings at exactly the end time
        // Add 1 second to ensure readings at exactly endDateTime are included
        LocalDateTime adjustedEndDateTime = endDateTime != null ? endDateTime.plusSeconds(1) : null;

        return readingRepository.findAllActiveWithFilters(spotId, success, startDateTime, adjustedEndDateTime, pageable);
    }

    @CacheEvict(value = {CacheConfig.READINGS_CACHE, CacheConfig.LATEST_READINGS_CACHE, CacheConfig.PAGINATED_READINGS_CACHE, CacheConfig.FILTERED_READINGS_CACHE}, allEntries = true)
    public List<Reading> saveAll(List<Reading> readings) {
        return readingRepository.saveAll(readings);
    }

    /**
     * Gets the latest successful reading for a spot.
     *
     * @param spot The spot to get the reading for.
     * @return The latest successful reading, or empty if none exists.
     */
    @Cacheable(value = CacheConfig.LATEST_READINGS_CACHE, key = "#spot.id")
    public Optional<Reading> getLatestReadingForSpot(Spot spot) {
        if (spot == null || spot.getId() == null) {
            return Optional.empty();
        }
        return readingRepository.findLatestSuccessfulReadingBySpotId(spot.getId());
    }
}
